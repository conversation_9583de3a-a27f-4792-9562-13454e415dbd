{% extends "base.html" %}

{% block content %}
<style>
    /* 完全复制index页面的样式 */
    .dashboard-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 80px);
        padding: 20px 0;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .header-top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-left {
        flex: 1;
    }

    .header-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
    }

    .user-info-row {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #fff;
        font-size: 14px;
    }

    .user-info-row .user-name {
        font-weight: 600;
        font-size: 16px;
    }

    .user-info-row .user-role {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.9;
    }

    .user-info-row .user-team {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.8;
        font-size: 13px;
    }

    .header-bottom-row {
        width: 100%;
    }

    .header-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .header-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .header-actions .dropdown {
        position: relative;
        display: inline-block;
    }

    /* 通知按钮青绿色样式 - 高优先级 */
    .btn-modern.btn-info-modern,
    button.btn-modern.btn-info-modern,
    #notificationDropdown.btn-modern.btn-info-modern {
        background: linear-gradient(135deg, #38f9d7 0%, #4dd0e1 100%) !important;
        color: white !important;
        position: relative;
        border: none !important;
    }

    .btn-modern.btn-info-modern:hover,
    button.btn-modern.btn-info-modern:hover,
    #notificationDropdown.btn-modern.btn-info-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 20px rgba(56, 249, 215, 0.4) !important;
        color: white !important;
        background: linear-gradient(135deg, #26e6cb 0%, #38f9d7 100%) !important;
    }

    .btn-modern {
        padding: 10px 20px;
        border-radius: 25px;
        border: none;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-danger-modern {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    .btn-danger-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
        color: white;
    }

    /* 筛选区域样式 */
    .filter-section-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filter-section-inline {
        padding: 25px;
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0;
    }

    .filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
        flex: 1;
    }

    .filter-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(102, 126, 234, 0.1);
        padding: 10px 15px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
        margin: 0;
    }

    .filter-checkbox:hover {
        background: rgba(102, 126, 234, 0.2);
        transform: translateY(-1px);
    }

    .filter-checkbox input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
        cursor: pointer;
        margin: 0;
    }

    /* 问题状态样式 */
    .bug-status {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
        border: 1px solid rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .status-待处理 {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
        border: none;
        box-shadow: 0 2px 10px rgba(240, 147, 251, 0.3);
    }

    .status-已分配 {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
        border: none;
        box-shadow: 0 2px 10px rgba(79, 172, 254, 0.3);
    }

    .status-处理中 {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        color: white;
        border: none;
        box-shadow: 0 2px 10px rgba(67, 233, 123, 0.3);
    }

    .status-已解决 {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
    }

    .status-已完成 {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        border: none;
        box-shadow: 0 2px 10px rgba(108, 117, 125, 0.3);
    }

    /* 问题列表区域 */
    .bugs-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .bugs-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
        position: relative;
        overflow: hidden;
    }

    .bugs-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    .bugs-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
        position: relative;
        z-index: 1;
    }

    .bugs-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 1;
    }

    /* 问题项样式 */
    .bug-item {
        background: white;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 25px;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    /* 三色交替背景色 - 明显的对比 */
    .bug-item:nth-child(3n+1) {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.12) 0%, rgba(118, 75, 162, 0.12) 100%);
        border-left: 3px solid rgba(102, 126, 234, 0.3);
    }

    .bug-item:nth-child(3n+2) {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.08) 0%, rgba(56, 249, 215, 0.08) 100%);
        border-left: 3px solid rgba(67, 233, 123, 0.3);
    }

    .bug-item:nth-child(3n+3) {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.08) 0%, rgba(245, 87, 108, 0.08) 100%);
        border-left: 3px solid rgba(240, 147, 251, 0.3);
    }

    .bug-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        filter: brightness(1.1) saturate(1.2);
    }

    .bug-item:nth-child(3n+1):hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.18) 0%, rgba(118, 75, 162, 0.18) 100%) !important;
        border-left: 3px solid rgba(102, 126, 234, 0.6) !important;
    }

    .bug-item:nth-child(3n+2):hover {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.15) 0%, rgba(56, 249, 215, 0.15) 100%) !important;
        border-left: 3px solid rgba(67, 233, 123, 0.6) !important;
    }

    .bug-item:nth-child(3n+3):hover {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.15) 0%, rgba(245, 87, 108, 0.15) 100%) !important;
        border-left: 3px solid rgba(240, 147, 251, 0.6) !important;
    }

    .bug-item:last-child {
        border-bottom: none;
        border-radius: 0 0 20px 20px;
    }

    .bug-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .bug-item:hover::before {
        transform: scaleY(1);
    }

    .bug-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        gap: 15px;
    }

    .bug-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        flex: 1;
        line-height: 1.4;
    }

    .bug-id {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        white-space: nowrap;
    }

    .bug-description {
        margin: 15px 0;
        padding: 12px 15px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
        border-radius: 8px;
        border-left: 3px solid #667eea;
        backdrop-filter: blur(5px);
    }

    .bug-description p {
        margin: 0;
        color: #5a6c7d;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .bug-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 15px;
        padding: 15px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
        border-radius: 12px;
        border-left: 4px solid #667eea;
        backdrop-filter: blur(5px);
    }

    .bug-meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #5a6c7d;
        font-size: 0.9rem;
    }

    .bug-meta-item i {
        color: #667eea;
        width: 16px;
        text-align: center;
    }

    /* 问题操作按钮样式 */
    .bug-actions {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
    }

    .btn-view {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a4190);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-assign {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .btn-assign:hover {
        background: linear-gradient(135deg, #e081e9, #e3455a);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-confirm {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
    }

    .btn-confirm:hover {
        background: linear-gradient(135deg, #3d9aec, #00e0ec);
        color: white;
        transform: translateY(-1px);
    }

    .btn-resolve {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        color: white;
    }

    .btn-resolve:hover {
        background: linear-gradient(135deg, #31d769, #26e7c5);
        color: white;
        transform: translateY(-1px);
    }

    .btn-complete {
        background: linear-gradient(135deg, #fa709a, #fee140);
        color: white;
    }

    .btn-complete:hover {
        background: linear-gradient(135deg, #e85e88, #ecd02e);
        color: white;
        transform: translateY(-1px);
    }

    .btn-delete {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #e55555, #dc4840);
        color: white;
        transform: translateY(-1px);
    }

    /* 空状态样式 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }

    .empty-icon {
        font-size: 4rem;
        color: #bdc3c7;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .empty-state p {
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .header-top-row {
            flex-direction: column;
            text-align: center;
        }

        .header-right {
            align-items: center;
        }

        .user-info-row {
            justify-content: center;
        }

        .header-actions {
            justify-content: center;
        }

        .filter-options {
            justify-content: center;
        }

        .filter-section-inline > div {
            flex-direction: column !important;
            align-items: center !important;
            gap: 15px !important;
        }

        .bug-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .bug-meta {
            justify-content: flex-start;
        }

        .bug-actions {
            justify-content: center;
        }

        .btn-action {
            flex: 1;
            justify-content: center;
            min-width: 100px;
        }

        .bugs-header {
            flex-direction: column;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .dashboard-header {
            padding: 20px;
        }

        .header-title {
            font-size: 2rem;
        }

        .filter-section-inline {
            padding: 20px;
        }

        .bug-item {
            padding: 20px;
        }

        .filter-options {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-checkbox {
            justify-content: center;
        }
    }

    /* 通知相关样式 */
    .notification-dropdown {
        min-width: 380px;
        max-width: 450px;
        max-height: 80vh;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        padding: 0;
        position: fixed !important;
        z-index: 10000 !important;
        margin: 0 !important;
        background: white;
        /* 默认隐藏，但不使用!important，让JavaScript可以覆盖 */
        display: none;
        overflow: visible;
    }

    /* 确保通知下拉菜单不被其他元素遮挡 */
    .notification-dropdown.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 动态调整通知位置的类 */
    .notification-dropdown.position-bottom {
        top: 100% !important;
        transform: translateX(-10px) translateY(10px) !important;
    }

    .notification-dropdown.position-top {
        top: 0 !important;
        transform: translateX(-10px) translateY(-100%) !important;
    }

    .notification-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding: 15px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .notification-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(3px);
    }

    .notification-item.unread {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-left: 4px solid #2196f3;
    }

    .notification-item.unread:hover {
        background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
    }

    .notification-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
        color: white;
        border-radius: 50%;
        padding: 4px 8px;
        font-size: 11px;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
    }

    /* 现代化状态徽章 */
    .badge.status-pending,
    .badge.status-待处理 {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(243, 156, 18, 0.3) !important;
    }

    .badge.status-assigned,
    .badge.status-已分配 {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3) !important;
    }

    .badge.status-resolving,
    .badge.status-处理中 {
        background: linear-gradient(135deg, #2ecc71, #27ae60) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3) !important;
    }

    .badge.status-resolved,
    .badge.status-已解决 {
        background: linear-gradient(135deg, #9b59b6, #8e44ad) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(155, 89, 182, 0.3) !important;
    }

    .badge.status-已确认 {
        background: linear-gradient(135deg, #27ae60, #229954) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(39, 174, 96, 0.3) !important;
    }

    .badge.status-已完成 {
        background: linear-gradient(135deg, #27ae60, #229954) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        box-shadow: 0 2px 10px rgba(39, 174, 96, 0.3) !important;
    }

    .badge {
        display: inline-block !important;
        padding: 8px 16px !important;
        font-size: 0.8rem !important;
        line-height: 1.2 !important;
        text-align: center !important;
        white-space: nowrap !important;
        vertical-align: baseline !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
    }

    .badge:hover {
        transform: translateY(-1px) !important;
    }

    /* 现代化问题列表 */
    .bugs-list-modern {
        display: grid;
        gap: 20px;
    }

    .bug-item-modern {
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .bug-item-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }

    .bug-item-modern:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0,0,0,0.15);
    }

    .bug-item-modern:hover::before {
        width: 8px;
    }

    .bug-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        gap: 15px;
    }

    .bug-title {
        flex: 1;
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1.4;
    }

    .bug-title a {
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .bug-title a:hover {
        color: #667eea;
        text-decoration: none;
    }

    .bug-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
        padding: 15px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        border-left: 4px solid #667eea;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #5a6c7d;
        font-size: 0.9rem;
    }

    .meta-item i {
        color: #667eea;
        width: 16px;
        text-align: center;
    }

    .bug-description {
        background: rgba(248, 249, 250, 0.8);
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 20px;
        border-left: 4px solid #e9ecef;
    }

    .bug-description strong {
        color: #2c3e50;
        font-weight: 600;
    }

    .bug-description-text {
        margin-top: 8px;
        color: #5a6c7d;
        line-height: 1.6;
        word-break: break-word;
    }

    .bug-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        align-items: center;
    }

    .btn-modern {
        padding: 10px 20px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-view-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-view-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-confirm-modern {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-confirm-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    }

    .btn-resolve-modern {
        background: linear-gradient(135deg, #2ecc71, #27ae60);
        color: white;
        box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    }

    .btn-resolve-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
    }

    .btn-modern:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    /* 空状态样式 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .empty-state i {
        font-size: 4rem;
        color: #bdc3c7;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #7f8c8d;
        font-size: 1.1rem;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .header-content {
            flex-direction: column;
            text-align: center;
        }

        .user-info-modern {
            flex-direction: column;
            text-align: center;
        }

        .filter-options {
            justify-content: center;
        }

        .bug-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .bug-actions {
            justify-content: center;
        }

        .btn-modern {
            flex: 1;
            justify-content: center;
            min-width: 120px;
        }
    }

    @media (max-width: 480px) {
        .team-issues-header {
            padding: 20px;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .status-filter-modern {
            padding: 20px;
        }

        .bug-item-modern {
            padding: 20px;
        }

        .filter-options {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-option {
            justify-content: center;
        }
    }

    /* 现代化Modal样式 */
    .modern-modal {
        border: none;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    .confirm-header {
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .confirm-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .confirm-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .confirm-animation {
        margin-bottom: 25px;
    }

    .confirm-icon-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        animation: pulse 2s infinite;
    }

    .confirm-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .confirm-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        display: flex;
        gap: 15px;
        justify-content: center;
    }

    .confirm-footer .btn-modern {
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .confirm-footer .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    }

    .confirm-footer .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .confirm-footer .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .confirm-footer .btn-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }
</style>

<!--
数据字段映射说明：
bug.id = 问题ID (数据库字段: id)
bug.title = 问题标题 (数据库字段: title)
bug.status = 当前状态 (数据库字段: status)
bug.creator = 提交人 (数据库字段: creator_id关联users表)
bug.assignee = 负责人 (数据库字段: assignee_id关联users表)
bug.created_at = 创建时间 (数据库字段: created_at)
-->

<div class="dashboard-container">
    <div class="container">
        <!-- 头部区域 -->
        <div class="dashboard-header">
            <div class="header-content">
                <!-- 第一行：标题和用户信息 -->
                <div class="header-top-row">
                    <div class="header-left">
                        <h1 class="header-title">
                            <i class="fas fa-tasks"></i>
                            我的问题列表
                        </h1>
                        <p class="header-subtitle">查看和管理分配给我的问题</p>
                    </div>

                    <div class="header-right">
                        <div class="user-info-row">
                            <span class="user-name">{{ user.chinese_name or user.username }}</span>
                            <span class="user-role">
                                {% if user.role_en == 'ssz' %}
                                    <i class="fas fa-tools"></i> 实施组人员
                                {% elif user.role_en == 'fzr' %}
                                    <i class="fas fa-user-tie"></i> 负责人
                                {% elif user.role_en == 'zncy' %}
                                    <i class="fas fa-user"></i> 组内成员
                                {% elif user.role_en == 'gly' %}
                                    <i class="fas fa-user-shield"></i> 管理员
                                {% else %}
                                    <i class="fas fa-user"></i> {{ user.role }}
                                {% endif %}
                            </span>
                            {% if user.team %}
                            <span class="user-team">
                                <i class="fas fa-building"></i> {{ user.team }}
                            </span>
                            {% endif %}
                        </div>

                        <!-- 通知设置链接 -->
                        <a href="/user/settings" class="btn btn-outline-light btn-sm" title="通知设置" style="margin-right: 10px;">
                            <i class="fas fa-bell-slash"></i>
                            通知设置
                        </a>

                        <div class="header-actions">
                            <!-- 通知按钮 -->
                            <div class="dropdown">
                                <button class="btn-modern btn-info-modern dropdown-toggle" type="button" id="notificationDropdown" aria-expanded="false" onclick="toggleNotificationDropdown(event); return false;" style="background: linear-gradient(135deg, #38f9d7 0%, #4dd0e1 100%) !important; border: none !important;">
                                    <i class="fas fa-bell"></i>
                                    通知
                                    <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-start notification-dropdown" aria-labelledby="notificationDropdown">
                                    <li class="dropdown-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 20px; margin: 0; border-radius: 15px 15px 0 0;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="fas fa-bell me-2"></i>
                                                <strong>通知中心</strong>
                                            </div>
                                            <button class="btn btn-sm btn-light" onclick="markAllAsRead(event)" style="border-radius: 20px; font-size: 11px;">
                                                <i class="fas fa-check-double me-1"></i>全部已读
                                            </button>
                                        </div>
                                    </li>
                                    <li style="padding: 0; margin: 0; background: white;">
                                        <div id="notificationList" style="max-height: 400px; overflow-y: auto; background: white;">
                                            <div class="text-center p-4">
                                                <i class="fas fa-spinner fa-spin text-primary"></i>
                                                <div class="mt-2 text-muted">加载中...</div>
                                            </div>
                                        </div>
                                    </li>
                                    <li style="border-top: 1px solid rgba(0,0,0,0.08); margin: 0; background: white;">
                                        <a class="dropdown-item text-center py-3" href="/notifications" style="color: #667eea; font-weight: 500;">
                                            <i class="fas fa-external-link-alt me-2"></i>查看全部通知
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <a href="/logout" class="btn-modern btn-danger-modern">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态筛选 -->
        <div class="filter-section-container">
            <div class="container">
                <div class="header-bottom-row">
                    <div class="filter-section-inline">
                        <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
                            <div class="filter-title">
                                <i class="fas fa-filter"></i>
                                筛选状态
                            </div>
                            <div class="filter-options">
                                <label class="filter-checkbox">
                                    <input type="checkbox" class="status-checkbox" value="已分配" checked>
                                    <span class="bug-status status-已分配">已分配</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" class="status-checkbox" value="处理中" checked>
                                    <span class="bug-status status-处理中">处理中</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" class="status-checkbox" value="已解决" checked>
                                    <span class="bug-status status-已解决">已解决</span>
                                </label>
                                <label class="filter-checkbox">
                                    <input type="checkbox" class="status-checkbox" value="已完成">
                                    <span class="bug-status status-已完成">已完成</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 问题列表 -->
        <div class="bugs-section">
            <div class="container">
                <div class="bugs-header">
                    <h2 class="bugs-title">
                        <i class="fas fa-list-ul"></i>
                        我的问题列表
                    </h2>
                    <div class="bugs-count">
                        <span id="visibleBugsCount">{{ bugs|length }}</span> 个问题
                    </div>
                </div>

                <div id="bugsList">
                    {% if bugs %}
                        {% for bug in bugs %}
                        <div class="bug-item" data-status="{{ bug.status }}" onclick="window.location.href='/bug/{{ bug.id }}'">
                            <div class="bug-header">
                                <h3 class="bug-title">{{ bug.title }}</h3>
                                <div class="bug-id">#{{ bug.id }}</div>
                            </div>

                            <!-- 问题描述 -->
                            {% if bug.description %}
                            <div class="bug-description">
                                <p>{{ bug.description[:100] }}{% if bug.description|length > 100 %}...{% endif %}</p>
                            </div>
                            {% endif %}

                            <div class="bug-meta">
                                <div class="bug-meta-item">
                                    <i class="fas fa-tag"></i>
                                    <span class="bug-status status-{{ bug.status }}">{{ bug.status }}</span>
                                </div>

                                <div class="bug-meta-item">
                                    <i class="fas fa-user"></i>
                                    <span>提交人: {{ bug.creator_name or '未知' }}</span>
                                </div>

                                {% if bug.assignee_name %}
                                <div class="bug-meta-item">
                                    <i class="fas fa-user-check"></i>
                                    <span>负责人: {{ bug.assignee_name }}</span>
                                </div>
                                {% endif %}

                                <div class="bug-meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>创建时间: {{ bug.local_created_at }}</span>
                                </div>

                                {% if bug.project %}
                                <div class="bug-meta-item">
                                    <i class="fas fa-folder"></i>
                                    <span>项目: {{ bug.project }}</span>
                                </div>
                                {% endif %}

                                {% if bug.department %}
                                <div class="bug-meta-item">
                                    <i class="fas fa-building"></i>
                                    <span>部门: {{ bug.department }}</span>
                                </div>
                                {% endif %}

                                {% if bug.priority %}
                                <div class="bug-meta-item">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <span>优先级: {{ bug.priority }}</span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="bug-actions">
                                <a href="/bug/{{ bug.id }}" class="btn-action btn-view">
                                    <i class="fas fa-eye"></i>
                                    查看详情
                                </a>

                                {% if bug.status == '已分配' %}
                                <button onclick="confirmReceive('{{ bug.id }}')" class="btn-action btn-confirm">
                                    <i class="fas fa-check"></i>
                                    确认接收
                                </button>
                                {% endif %}

                                {% if bug.status == '处理中' %}
                                <a href="/bug/resolve/{{ bug.id }}" class="btn-action btn-resolve">
                                    <i class="fas fa-tools"></i>
                                    解决问题
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h3>暂无问题</h3>
                            <p>当前没有分配给您的问题</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 现代化确认Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header confirm-header">
                <div class="confirm-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
            </div>
            <div class="modal-body confirm-body">
                <div class="confirm-animation">
                    <div class="confirm-icon-large">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <p class="confirm-message" id="confirmModalMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer confirm-footer">
                <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn-modern btn-primary" id="confirmModalBtn">
                    <i class="fas fa-check me-2"></i>确认
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 现代化Modal函数
function showConfirmModal(title, message, onConfirm) {
    document.getElementById('confirmModalTitle').textContent = title;
    document.getElementById('confirmModalMessage').textContent = message;

    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();

    // 绑定确认按钮事件
    document.getElementById('confirmModalBtn').onclick = function() {
        modal.hide();
        if (onConfirm) onConfirm();
    };
}

// 确认接收功能
function confirmReceive(bugId) {
    event.stopPropagation(); // 阻止事件冒泡

    // 使用现代化确认弹窗
    showConfirmModal('确认接收', '确认接收这个问题吗？', function() {
        performConfirmReceive(bugId);
    });
}

function performConfirmReceive(bugId) {

    fetch(`/bug/confirm/${bugId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('问题已确认接收！', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification('操作失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('网络错误，请稍后重试', 'error');
    });
}

// 通知函数
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification-toast notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 15px 20px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        animation: slideInRight 0.3s ease-out;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    `;

    const backgrounds = {
        success: 'linear-gradient(135deg, #2ecc71, #27ae60)',
        error: 'linear-gradient(135deg, #e74c3c, #c0392b)',
        info: 'linear-gradient(135deg, #3498db, #2980b9)',
        warning: 'linear-gradient(135deg, #f39c12, #e67e22)'
    };

    notification.style.background = backgrounds[type] || backgrounds.info;

    const icon = type === 'error' ? 'fas fa-exclamation-circle' :
                type === 'success' ? 'fas fa-check-circle' :
                type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="${icon}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }
    }, 3000);
}

// 通知中心位置调整
function adjustNotificationPosition() {
    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) return;

    console.log('Adjusting notification position...');

    // 获取按钮位置和视窗信息
    const buttonRect = button.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const dropdownWidth = 380;
    const maxDropdownHeight = 300;

    console.log('Button rect:', buttonRect);
    console.log('Viewport:', viewportWidth, 'x', viewportHeight);

    // 计算最佳位置
    let left = buttonRect.right - dropdownWidth;
    let top = buttonRect.bottom + 10;

    // 确保不超出右边界
    if (left < 10) {
        left = 10;
    }

    // 确保不超出左边界
    if (left + dropdownWidth > viewportWidth - 10) {
        left = viewportWidth - dropdownWidth - 10;
    }

    // 检查下方空间
    if (top + maxDropdownHeight > viewportHeight - 10) {
        // 下方空间不足，显示在上方
        top = buttonRect.top - maxDropdownHeight - 10;
        if (top < 10) {
            // 上方空间也不足，显示在按钮右侧
            left = buttonRect.right + 10;
            top = buttonRect.top;
            if (left + dropdownWidth > viewportWidth - 10) {
                // 右侧空间不足，显示在按钮左侧
                left = buttonRect.left - dropdownWidth - 10;
            }
        }
    }

    console.log('Setting position:', left, top);

    // 应用位置
    dropdown.style.left = left + 'px';
    dropdown.style.top = top + 'px';
    dropdown.style.right = 'auto';
    dropdown.style.bottom = 'auto';
}

// 全局变量
let notificationDropdownOpen = false;

// 切换通知下拉菜单
function toggleNotificationDropdown(event) {
    // 强制阻止所有事件传播
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    console.log('toggleNotificationDropdown called');

    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) {
        console.log('Elements not found');
        return false;
    }

    // 检查当前实际显示状态
    const computedStyle = window.getComputedStyle(dropdown);
    const isCurrentlyVisible = computedStyle.display !== 'none';

    console.log('Current visibility:', isCurrentlyVisible);

    if (isCurrentlyVisible) {
        // 关闭下拉菜单
        console.log('Closing dropdown');
        dropdown.style.display = 'none';
        button.setAttribute('aria-expanded', 'false');
        notificationDropdownOpen = false;
    } else {
        // 打开下拉菜单
        console.log('Opening dropdown');
        dropdown.style.display = 'block';
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        dropdown.style.zIndex = '10000';
        dropdown.style.position = 'fixed';
        button.setAttribute('aria-expanded', 'true');
        notificationDropdownOpen = true;

        // 调整位置
        setTimeout(function() {
            adjustNotificationPosition();
        }, 100);

        // 加载通知
        loadNotifications();
    }

    return false;
}

function loadNotifications() {
    const notificationList = document.getElementById('notificationList');

    fetch('/api/notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications);
                updateNotificationBadge(data.unread_count);
            } else {
                notificationList.innerHTML = '<div class="text-center p-4 text-muted">加载失败</div>';
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationList.innerHTML = '<div class="text-center p-4 text-muted">加载失败</div>';
        });
}

function displayNotifications(notifications) {
    const notificationList = document.getElementById('notificationList');

    if (notifications.length === 0) {
        notificationList.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-bell-slash text-muted" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <div class="text-muted">暂无通知</div>
            </div>
        `;
        return;
    }

    notificationList.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.is_read ? '' : 'unread'}" style="padding: 15px 20px; border-bottom: 1px solid rgba(0,0,0,0.05); cursor: pointer;" onclick="markAsRead(${notification.id})">
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <div style="flex-shrink: 0; width: 8px; height: 8px; border-radius: 50%; background: ${notification.is_read ? '#ddd' : '#667eea'}; margin-top: 6px;"></div>
                <div style="flex: 1;">
                    <div style="font-weight: ${notification.is_read ? 'normal' : '600'}; color: ${notification.is_read ? '#666' : '#333'}; margin-bottom: 4px;">
                        ${notification.title}
                    </div>
                    <div style="font-size: 0.9rem; color: #666; line-height: 1.4; margin-bottom: 6px;">
                        ${notification.content}
                    </div>
                    <div style="font-size: 0.8rem; color: #999;">
                        ${notification.created_at}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadge');
    if (count > 0) {
        badge.textContent = count;
        badge.style.display = 'inline-block';
    } else {
        badge.style.display = 'none';
    }
}

function markAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllAsRead(event) {
    event.stopPropagation();

    fetch('/api/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
}

// 筛选功能
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.status-checkbox');

    function filterBugs() {
        const selectedStatuses = Array.from(checkboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value);

        const bugItems = document.querySelectorAll('.bug-item');
        let visibleCount = 0;

        bugItems.forEach(item => {
            const status = item.dataset.status;

            if (selectedStatuses.includes(status)) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // 更新计数
        document.getElementById('visibleBugsCount').textContent = visibleCount;
    }

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const option = this.closest('.filter-checkbox');

            // 更新选项样式
            if (this.checked) {
                option.style.background = 'rgba(102, 126, 234, 0.2)';
                option.style.borderColor = '#667eea';
            } else {
                option.style.background = 'rgba(102, 126, 234, 0.1)';
                option.style.borderColor = 'transparent';
            }

            filterBugs();
        });

        // 初始化选项样式
        const option = checkbox.closest('.filter-checkbox');
        if (checkbox.checked) {
            option.style.background = 'rgba(102, 126, 234, 0.2)';
            option.style.borderColor = '#667eea';
        }
    });

    // 初始过滤
    filterBugs();

    // 点击外部关闭通知下拉菜单
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('.notification-dropdown');
        const button = document.getElementById('notificationDropdown');

        if (dropdown && !dropdown.contains(event.target) && !button.contains(event.target)) {
            dropdown.classList.remove('show');
        }
    });

    // 阻止问题项点击时的按钮事件冒泡
    document.querySelectorAll('.bug-actions .btn-action').forEach(button => {
        button.addEventListener('click', function(event) {
            event.stopPropagation();
        });
    });
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .filter-checkbox {
        transition: all 0.3s ease !important;
    }

    .bug-item {
        transition: all 0.3s ease;
    }

    .notification-item.unread {
        background: rgba(102, 126, 234, 0.05) !important;
    }

    .notification-item:hover {
        background: rgba(102, 126, 234, 0.1) !important;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
