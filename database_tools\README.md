# ReBugTracker 数据库工具集

## 🚀 快速开始

### 方式1: 批处理快速启动 (推荐)
```bash
# Windows 双击运行
quick_start.bat
```

### 方式2: 交互式工具选择
```bash
python tool_index.py
```

## 📋 核心工具

### 🔄 数据库同步

**`smart_sync_postgres_to_sqlite.py`** - 智能同步（推荐）
- ✅ **自动检查** - 检查表结构兼容性
- ✅ **智能决策** - 根据检查结果决定同步方式
- ✅ **完整同步** - 必要时重建表结构+同步数据
- ✅ **增量同步** - 表结构兼容时只同步数据
- ✅ **自动备份** - 操作前自动备份
- ✅ **孤儿过滤** - 自动过滤无效数据
- ✅ **一键完成** - 集成了所有同步功能

**`sync_sqlite_to_postgres_data.py`** - 反向同步
- 🔄 从SQLite同步数据到PostgreSQL
- 适用于本地开发后同步到服务器

### 🔍 数据库检查

**`sync_status_checker.py`** - 全面状态检查（推荐）
- ✅ **连接测试** - 验证数据库连接
- ✅ **表结构对比** - 详细的结构差异分析
- ✅ **数据量统计** - 各表数据量对比
- ✅ **同步状态分析** - 判断是否需要同步
- ✅ **一次检查** - 全面诊断所有问题

**`table_structure_checker.py`** - 专门表结构对比
- 🔍 详细的字段级对比分析
- 🔍 类型兼容性检查
- 🔍 问题诊断和建议

## 🎯 常用场景

### 场景1: 首次设置SQLite
```bash
python database_tools/sync_tools/smart_sync_postgres_to_sqlite.py
```

### 场景2: 定期检查和同步
```bash
# 1. 检查状态
python database_tools/check_tools/sync_status_checker.py

# 2. 如果需要，执行同步
python database_tools/sync_tools/smart_sync_postgres_to_sqlite.py
```

### 场景3: 解决登录问题
```bash
# 一键修复（会检查并修复表结构）
python database_tools/sync_tools/smart_sync_postgres_to_sqlite.py
```

## 🚨 故障排除

| 问题 | 解决方案 |
|------|----------|
| 登录失败 | 运行智能同步工具 |
| 表结构不一致 | 运行智能同步工具 |
| 数据不同步 | 先检查状态，再运行同步 |

## 🔧 工具区别说明

### 同步工具对比

| 功能 | 智能同步 | 反向同步 |
|------|----------|----------|
| 方向 | PostgreSQL → SQLite | SQLite → PostgreSQL |
| 表结构检查 | ✅ 自动检查 | ❌ 不检查 |
| 表结构重建 | ✅ 必要时重建 | ❌ 不重建 |
| 数据同步 | ✅ 完整同步 | ✅ 数据同步 |
| 自动备份 | ✅ 自动备份 | ❌ 无备份 |
| 推荐场景 | 日常使用、问题修复 | 开发环境同步 |

### 检查工具对比

| 功能 | 全面状态检查 | 表结构对比 | PostgreSQL详细检查 🆕 | SQLite详细检查 🆕 | 结构验证工具 🆕 |
|------|-------------|------------|---------------------|------------------|----------------|
| 连接测试 | ✅ 包含 | ❌ 不包含 | ❌ 不包含 | ❌ 不包含 | ✅ 包含 |
| 表结构对比 | ✅ 基础对比 | ✅ 详细对比 | ❌ 单库检查 | ❌ 单库检查 | ✅ 规范检查 |
| 数据量统计 | ✅ 包含 | ❌ 不包含 | ✅ 包含 | ✅ 包含 | ❌ 不包含 |
| 外键约束 | ❌ 不包含 | ❌ 不包含 | ✅ 详细分析 | ✅ 详细分析 | ❌ 不包含 |
| 索引信息 | ❌ 不包含 | ❌ 不包含 | ✅ 详细分析 | ✅ 详细分析 | ❌ 不包含 |
| 创建语句 | ❌ 不包含 | ❌ 不包含 | ❌ 不包含 | ✅ 显示SQL | ❌ 不包含 |
| 同步建议 | ✅ 提供建议 | ✅ 提供建议 | ❌ 不提供 | ❌ 不提供 | ✅ 验证报告 |
| 推荐场景 | 日常检查 | 结构问题诊断 | PG深度分析 | SQLite深度分析 | 部署验证 |

### 🆕 新增工具说明

**postgres_structure_inspector.py** - PostgreSQL详细结构检查
- 🔍 完整的字段信息（类型、长度、约束、默认值）
- 🔗 外键约束详细分析（删除/更新规则）
- 📊 索引信息（主键、唯一索引、普通索引）
- 📈 记录数统计

**sqlite_structure_inspector.py** - SQLite详细结构检查
- 🔍 完整的字段信息和约束
- 🔗 外键约束分析
- 📊 索引信息和自动索引
- 📝 完整的CREATE语句
- 💾 数据库大小统计

**database_structure_validator.py** - 数据库结构验证
- ✅ 验证必需表的存在性
- 🔍 检查关键字段类型和约束
- 📋 验证默认值设置
- 📊 生成规范性报告
- 🎯 适用于部署前验证和质量保证

### 历史工具说明

**为什么删除了其他同步工具？**

之前存在多个专门工具：
- `full_sync` - 完整同步（完全重建）
- `fixed_sync` - 修复同步（针对特定问题）
- `data_sync` - 数据同步（只同步数据）
- `schema_sync` - 结构同步（只同步表结构）

**问题：**
- 用户需要判断用哪个工具
- 容易选错导致问题
- 需要多步操作

**解决方案：**
现在的智能同步工具集成了所有功能，自动判断需要什么类型的同步。

## 💡 设计原则

- **一键解决** - 主要工具都是一键完成，不需要分步操作
- **自动检查** - 同步前自动检查表结构兼容性
- **安全备份** - 重要操作前自动备份
- **全面诊断** - 检查工具提供完整的状态分析

---

💡 **记住**: 大部分问题都可以通过运行智能同步工具解决！
