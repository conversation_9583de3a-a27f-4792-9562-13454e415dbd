<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题详情 - ReBugTracker</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-left .subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 现代化按钮样式 */
        .btn-modern {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
            color: white;
        }

        .btn-danger-modern {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .btn-success-modern {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .btn-success-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
            color: white;
        }

        .btn-warning-modern {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-warning-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(243, 156, 18, 0.4);
            color: white;
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        .bug-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .bug-meta {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }

        .meta-item {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-right: 20px;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .meta-item i {
            color: #667eea;
            width: 16px;
        }

        /* 优先级图标特殊颜色 */
        .meta-item i.fa-exclamation-circle {
            color: #f39c12;
        }

        /* 项目图标特殊颜色 */
        .meta-item i.fa-folder {
            color: #3498db;
        }

        /* 部门图标特殊颜色 */
        .meta-item i.fa-building {
            color: #9b59b6;
        }

        /* 现代化徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            font-size: 0.85rem;
            font-weight: 600;
            border-radius: 20px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .badge.status-pending, .badge.status-待处理 {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .badge.status-assigned, .badge.status-已分配 {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .badge.status-resolving, .badge.status-处理中 {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }

        .badge.status-resolved, .badge.status-已解决 {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
        }

        .badge.status-已确认 {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .badge.status-已完成 {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        /* 内容卡片样式 */
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 12px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .section-title i {
            color: #667eea;
        }

        .bug-description {
            line-height: 1.8;
            color: #555;
            font-size: 1rem;
            background: rgba(102, 126, 234, 0.05);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        /* 附件样式 */
        .attachment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .attachment-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .attachment-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .attachment-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }

        .attachment-item a {
            display: block;
            padding: 15px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .attachment-item a:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #5a67d8;
        }

        /* 解决方案样式 */
        .bug-resolution {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #2ecc71;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.1);
        }

        .resolution-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .resolution-content {
            line-height: 1.8;
            color: #555;
            font-size: 1rem;
        }

        /* 操作按钮区域 */
        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            padding: 25px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        /* 消息提示样式 */
        .alert {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 12px;
            padding: 15px 20px;
            margin-top: 20px;
            color: #2980b9;
            font-weight: 500;
        }

        /* 现代化Modal样式 */
        .modern-modal {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        /* 确认Modal样式 */
        .confirm-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .confirm-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .confirm-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .confirm-animation {
            margin-bottom: 25px;
        }

        .confirm-icon-large {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
            animation: pulse 2s infinite;
        }

        .confirm-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .confirm-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .confirm-footer .btn-modern {
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .confirm-footer .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        }

        .confirm-footer .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .confirm-footer .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .confirm-footer .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .confirm-delete-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .confirm-delete-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .confirm-delete-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .warning-animation {
            margin-bottom: 25px;
        }

        .warning-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
            animation: pulse 2s infinite;
        }

        .confirm-delete-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .warning-text {
            color: #dc3545;
            font-weight: 500;
            margin: 0;
        }

        .confirm-delete-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .confirm-delete-footer .btn-modern {
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .confirm-delete-footer .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        }

        .confirm-delete-footer .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .confirm-delete-footer .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .confirm-delete-footer .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* 成功Modal样式 */
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .success-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .success-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .success-animation {
            margin-bottom: 25px;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #28a745;
            stroke-miterlimit: 10;
            margin: 0 auto 20px;
            box-shadow: inset 0px 0px 0px #28a745;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 2;
            stroke-miterlimit: 10;
            stroke: #28a745;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        .success-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .success-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            text-align: center;
        }

        .success-footer .btn-modern {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .success-footer .btn-modern:hover {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        /* 错误Modal样式 */
        .error-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .error-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .error-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .error-animation {
            margin-bottom: 25px;
        }

        .error-icon-large {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .error-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .error-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            text-align: center;
        }

        .error-footer .btn-modern {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .error-footer .btn-modern:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        /* 动画效果 */
        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {
            0%, 100% {
                transform: none;
            }
            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 30px #28a745;
            }
        }

        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            10%, 30%, 50%, 70%, 90% {
                transform: translateX(-5px);
            }
            20%, 40%, 60%, 80% {
                transform: translateX(5px);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .content {
                padding: 20px;
            }

            .bug-title {
                font-size: 1.5rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .attachment-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-bug"></i>
                    问题详情
                </h1>
                <div class="subtitle">
                    当前用户: {{ user.chinese_name or user.username }} ({{ user.role }})
                </div>
            </div>
            <div class="header-actions">
                <a href="/" class="btn-modern btn-primary-modern">
                    <i class="fas fa-arrow-left"></i>
                    返回问题列表
                </a>
                <a href="/logout" class="btn-modern btn-danger-modern">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 问题标题和状态 -->
            <div class="bug-title">
                <span>#{{ bug.id }} - {{ bug.title }}</span>
                <span class="badge status-{{ bug.status.lower().replace(' ', '-') }}">
                    <i class="fas fa-circle"></i>
                    {{ bug.status }}
                </span>
            </div>

            <!-- 问题元信息 -->
            <div class="bug-meta">
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span>提交人: {{ bug.creator_name }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-calendar-plus"></i>
                    <span>创建时间: {{ bug.local_created_at }}</span>
                </div>
                {% if bug.assignee_name %}
                <div class="meta-item">
                    <i class="fas fa-user-check"></i>
                    <span>负责人: {{ bug.assignee_name }}</span>
                </div>
                {% endif %}
                {% if bug.project %}
                <div class="meta-item">
                    <i class="fas fa-folder"></i>
                    <span>项目: {{ bug.project }}</span>
                </div>
                {% endif %}
                {% if bug.department %}
                <div class="meta-item">
                    <i class="fas fa-building"></i>
                    <span>部门: {{ bug.department }}</span>
                </div>
                {% endif %}
                {% if bug.priority %}
                <div class="meta-item">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>优先级: {{ bug.priority }}</span>
                </div>
                {% endif %}
                {% if bug.local_resolved_at %}
                <div class="meta-item">
                    <i class="fas fa-calendar-check"></i>
                    <span>解决时间: {{ bug.local_resolved_at }}</span>
                </div>
                {% endif %}
            </div>

            <!-- 问题描述 -->
            <div class="content-card">
                <div class="section-title">
                    <i class="fas fa-file-alt"></i>
                    问题描述
                </div>
                <div class="bug-description">
                    {{ bug.description|replace('\n', '<br>')|safe }}
                </div>
            </div>

            <!-- 附件 -->
            {% if images %}
            <div class="content-card">
                <div class="section-title">
                    <i class="fas fa-paperclip"></i>
                    附件 ({{ images|length }} 张图片)
                </div>
                <div class="attachment-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">
                    {% for image in images %}
                    <div class="attachment-item" style="position: relative; border-radius: 8px; overflow: hidden; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                        <img src="{{ image.path }}" alt="问题截图"
                             style="width: 100%; height: 150px; object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                             onclick="showImage('{{ image.path }}')">
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.7); color: white; padding: 5px; font-size: 12px; text-align: center;">
                            图片 {{ loop.index }}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 自定义图片查看器 -->
                <div id="customImageViewer" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255,255,255,0.95); z-index: 9999; overflow: hidden;">
                    <div style="position: absolute; top: 20px; right: 20px; z-index: 10000;">
                        <button id="closeImageBtn" type="button" style="background-color: #dc3545; color: white; border: none; border-radius: 50%; width: 40px; height: 40px; font-size: 20px; cursor: pointer; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="imageContainer" style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; padding: 20px; overflow: hidden;">
                        <img id="viewerImage" src="" alt="问题截图" style="max-width: 95%; max-height: 90vh; object-fit: contain; cursor: grab; transition: transform 0.2s ease;">
                    </div>
                    <div style="position: absolute; bottom: 20px; left: 0; right: 0; text-align: center; color: #333; font-size: 14px;">
                        <i class="fas fa-info-circle me-1"></i>点击图片、背景或按ESC键关闭 | 滚轮缩放 | 拖拽移动
                    </div>
                    <div id="zoomInfo" style="position: absolute; top: 70px; right: 20px; color: #333; background: rgba(240,240,240,0.8); padding: 8px 12px; border-radius: 4px; font-size: 14px; display: none;">
                        缩放: <span id="zoomLevel">100%</span>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 解决方案 -->
            {% if bug.resolution %}
            <div class="content-card">
                <div class="section-title">
                    <i class="fas fa-lightbulb"></i>
                    解决方案
                </div>
                <div class="bug-resolution">
                    <div class="resolution-title">
                        <i class="fas fa-check-circle"></i>
                        解决方案详情
                    </div>
                    <div class="resolution-content">
                        {{ bug.resolution|replace('\n', '<br>')|safe }}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 操作按钮 -->
            <div class="action-buttons">
                {% if user.role_en == 'fzr' %}
                <a href="/bug/assign/{{ bug.id }}" class="btn-modern btn-success-modern">
                    <i class="fas fa-user-plus"></i>
                    指派问题
                </a>
                {% endif %}

                {% if user.role_en == 'ssz' and bug.status == '已解决' and user.id == created_by %}
                <button onclick="confirmComplete('{{ bug.id }}')" class="btn-modern btn-warning-modern">
                    <i class="fas fa-check-circle"></i>
                    确认闭环
                </button>
                {% endif %}

                {% if user.role_en == 'gly' or (user.role_en == 'ssz' and user.id == created_by) %}
                <button onclick="deleteBug('{{ bug.id }}')" class="btn-modern btn-danger-modern">
                    <i class="fas fa-trash-alt"></i>
                    删除问题
                </button>
                {% endif %}
            </div>

            <!-- 消息提示 -->
            {% if message %}
            <div class="alert">
                <i class="fas fa-info-circle"></i>
                {{ message }}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 现代化确认Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header confirm-header">
                    <div class="confirm-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
                </div>
                <div class="modal-body confirm-body">
                    <div class="confirm-animation">
                        <div class="confirm-icon-large">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <p class="confirm-message" id="confirmModalMessage">确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer confirm-footer">
                    <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="button" class="btn-modern btn-primary" id="confirmModalBtn">
                        <i class="fas fa-check me-2"></i>确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化确认删除Modal -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header confirm-delete-header">
                    <div class="confirm-delete-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h5 class="modal-title">确认删除</h5>
                </div>
                <div class="modal-body confirm-delete-body">
                    <div class="warning-animation">
                        <div class="warning-icon">
                            <i class="fas fa-trash"></i>
                        </div>
                    </div>
                    <p class="confirm-delete-message">确定要删除这个问题吗？</p>
                    <p class="warning-text">此操作不可撤销！</p>
                </div>
                <div class="modal-footer confirm-delete-footer">
                    <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="button" class="btn-modern btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化成功提示Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header success-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5 class="modal-title" id="successModalTitle">操作成功！</h5>
                </div>
                <div class="modal-body success-body">
                    <div class="success-animation">
                        <div class="checkmark">
                            <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                                <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                            </svg>
                        </div>
                    </div>
                    <p class="success-message" id="successModalMessage">操作已成功完成</p>
                </div>
                <div class="modal-footer success-footer">
                    <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化错误提示Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header error-header">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h5 class="modal-title">操作失败</h5>
                </div>
                <div class="modal-body error-body">
                    <div class="error-animation">
                        <div class="error-icon-large">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                    <p class="error-message" id="errorModalMessage">操作失败，请稍后重试</p>
                </div>
                <div class="modal-footer error-footer">
                    <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // 确认闭环函数
    function confirmComplete(bugId) {
        // 设置Modal内容
        document.getElementById('confirmModalTitle').textContent = '确认闭环';
        document.getElementById('confirmModalMessage').textContent = '确认此问题已完成闭环？';

        // 修改图标为闭环图标
        document.querySelector('#confirmModal .confirm-icon-large i').className = 'fas fa-check-circle';

        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        modal.show();

        // 绑定确认按钮事件
        document.getElementById('confirmModalBtn').onclick = function() {
            modal.hide();

            fetch('/bug/complete/' + bugId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(function(response) {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            }).then(function(data) {
                if(data.success) {
                    // 显示成功Modal
                    document.getElementById('successModalTitle').textContent = '确认闭环成功！';
                    document.getElementById('successModalMessage').textContent = '问题已成功完成闭环';
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();

                    // 2秒后刷新页面
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    // 显示错误Modal
                    document.getElementById('errorModalMessage').textContent = '确认闭环失败: ' + data.message;
                    const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                    errorModal.show();
                }
            }).catch(function(error) {
                // 显示错误Modal
                document.getElementById('errorModalMessage').textContent = '确认闭环请求失败: ' + error.message;
                const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                errorModal.show();
            });
        };
    }

    // 删除问题函数
    function deleteBug(bugId) {
        const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
        modal.show();

        // 绑定确认删除按钮事件
        document.getElementById('confirmDeleteBtn').onclick = function() {
            modal.hide();

            fetch('/bug/delete/' + bugId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            }).then(function(response) {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            }).then(function(data) {
                if(data.success) {
                    // 显示成功Modal
                    document.getElementById('successModalTitle').textContent = '删除成功！';
                    document.getElementById('successModalMessage').textContent = '问题已成功删除';
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();

                    // 2秒后跳转到首页
                    setTimeout(function() {
                        window.location.href = '/?message=问题已成功删除';
                    }, 2000);
                } else {
                    // 显示错误Modal
                    document.getElementById('errorModalMessage').textContent = '删除失败: ' + data.message;
                    const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                    errorModal.show();
                }
            }).catch(function(error) {
                // 显示错误Modal
                document.getElementById('errorModalMessage').textContent = '删除请求失败: ' + error.message;
                const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                errorModal.show();
            });
        };
    }
    // 自定义图片查看器
    let currentScale = 1;
    let isDragging = false;
    let startX, startY, translateX = 0, translateY = 0;

    function showImage(imageSrc) {
        const viewer = document.getElementById('customImageViewer');
        const image = document.getElementById('viewerImage');
        const zoomInfo = document.getElementById('zoomInfo');

        // 重置缩放和位置
        currentScale = 1;
        translateX = 0;
        translateY = 0;
        updateImageTransform();

        image.src = imageSrc;
        viewer.style.display = 'block';
        zoomInfo.style.display = 'none';

        // 禁止页面滚动
        document.body.style.overflow = 'hidden';
    }

    function hideImage() {
        const viewer = document.getElementById('customImageViewer');
        viewer.style.display = 'none';

        // 恢复页面滚动
        document.body.style.overflow = 'auto';
    }

    function updateImageTransform() {
        const image = document.getElementById('viewerImage');
        const zoomLevel = document.getElementById('zoomLevel');

        image.style.transform = `translate(${translateX}px, ${translateY}px) scale(${currentScale})`;
        zoomLevel.textContent = `${Math.round(currentScale * 100)}%`;
    }

    function showZoomInfo() {
        const zoomInfo = document.getElementById('zoomInfo');
        zoomInfo.style.display = 'block';

        // 3秒后隐藏缩放信息
        clearTimeout(window.zoomInfoTimeout);
        window.zoomInfoTimeout = setTimeout(() => {
            zoomInfo.style.display = 'none';
        }, 3000);
    }

    // 绑定事件
    document.addEventListener('DOMContentLoaded', function() {
        const viewer = document.getElementById('customImageViewer');
        const closeBtn = document.getElementById('closeImageBtn');
        const image = document.getElementById('viewerImage');
        const container = document.getElementById('imageContainer');

        // 点击关闭按钮
        closeBtn.addEventListener('click', hideImage);

        // 点击背景关闭
        viewer.addEventListener('click', function(e) {
            if (e.target === viewer || e.target === container) {
                hideImage();
            }
        });

        // 点击图片关闭 - 改为双击关闭，单击不关闭
        image.addEventListener('dblclick', hideImage);

        // 阻止单击图片事件冒泡，避免点击图片时关闭查看器
        image.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 滚轮缩放
        container.addEventListener('wheel', function(e) {
            e.preventDefault();

            // 确定缩放方向和缩放步长
            const delta = e.deltaY > 0 ? -0.1 : 0.1;

            // 限制缩放范围
            const newScale = Math.max(0.1, Math.min(5, currentScale + delta));

            // 计算缩放前后的差值，用于调整位置
            const scaleDiff = newScale - currentScale;

            // 计算鼠标相对于图片的位置
            const rect = image.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            // 根据鼠标位置调整图片位置，实现以鼠标为中心的缩放
            translateX -= (mouseX * scaleDiff) / currentScale;
            translateY -= (mouseY * scaleDiff) / currentScale;

            // 更新缩放比例
            currentScale = newScale;

            // 更新图片变换
            updateImageTransform();

            // 显示缩放信息
            showZoomInfo();
        });

        // 拖拽移动
        image.addEventListener('mousedown', function(e) {
            e.preventDefault();
            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            image.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            translateX = e.clientX - startX;
            translateY = e.clientY - startY;
            updateImageTransform();
        });

        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                image.style.cursor = 'grab';
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && viewer.style.display === 'block') {
                hideImage();
            }
        });
    });
    </script>
</body>
</html>
