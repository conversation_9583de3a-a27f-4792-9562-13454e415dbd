# 忽略版本控制文件
.git
.gitignore

# 忽略本地配置文件
.env
*.env.local

# 忽略日志文件
*.log
logs/

# 忽略临时文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 忽略测试相关
tests/
test/
test_*.py
debug_*.html
database_tools/
.coverage
htmlcov/
.pytest_cache/

# 忽略IDE文件
.vscode/
.idea/
*.swp
*.swo

# 忽略运行时生成的目录
uploads/
*.db
*.sqlite
*.sqlite3

# 忽略文档
README.md
*.md
docs/

# 忽略Docker相关文件
docker-compose*.yml
.dockerignore
