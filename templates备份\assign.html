<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分配问题 - ReBugTracker</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-left .subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 现代化按钮样式 */
        .btn-modern {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
            color: white;
        }

        .btn-danger-modern {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .btn-success-modern {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .btn-success-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
            color: white;
        }

        .btn-secondary-modern {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .btn-secondary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(149, 165, 166, 0.4);
            color: white;
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        /* 内容卡片样式 */
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 12px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .section-title i {
            color: #667eea;
        }

        .bug-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .bug-meta {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }

        .meta-item {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-right: 20px;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .meta-item i {
            color: #667eea;
            width: 16px;
        }

        /* 现代化徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            font-size: 0.85rem;
            font-weight: 600;
            border-radius: 20px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .badge.status-pending, .badge.status-待处理 {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .badge.status-assigned, .badge.status-已分配 {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .badge.status-resolving, .badge.status-处理中 {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }

        .badge.status-resolved, .badge.status-已解决 {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
        }

        .badge.status-已确认 {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .badge.status-已完成 {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .bug-description {
            line-height: 1.8;
            color: #555;
            font-size: 1rem;
            background: rgba(102, 126, 234, 0.05);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 操作按钮区域 */
        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            padding: 25px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        /* 权限提示样式 */
        .permission-notice {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            color: #c0392b;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .content {
                padding: 20px;
            }

            .bug-title {
                font-size: 1.5rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        /* 现代化Modal样式 */
        .modern-modal {
            border: none;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        /* 确认Modal样式 */
        .confirm-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .confirm-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: bounceIn 0.6s ease-out;
        }

        .confirm-body {
            padding: 30px;
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
        }

        .confirm-message {
            font-size: 1.1rem;
            color: #2c3e50;
            margin: 0;
            line-height: 1.6;
        }

        .confirm-footer {
            padding: 20px 30px;
            background: rgba(248, 249, 250, 0.8);
            border: none;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        /* 确认和取消按钮的不同样式 */
        .confirm-footer .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .confirm-footer .btn-primary:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .confirm-footer .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .confirm-footer .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            color: white;
        }

        /* 成功Modal样式 */
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .success-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: bounceIn 0.6s ease-out;
        }

        .success-body {
            padding: 30px;
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
        }

        .success-message {
            font-size: 1.1rem;
            color: #2c3e50;
            margin: 20px 0 0 0;
            line-height: 1.6;
        }

        .success-footer {
            padding: 20px 30px;
            background: rgba(248, 249, 250, 0.8);
            border: none;
            display: flex;
            justify-content: center;
        }

        .success-footer .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .success-footer .btn-primary:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        /* 警告Modal样式 */
        .alert-header {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            border: none;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .alert-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: bounceIn 0.6s ease-out;
        }

        .alert-body {
            padding: 30px;
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
        }

        .alert-message {
            font-size: 1.1rem;
            color: #2c3e50;
            margin: 0;
            line-height: 1.6;
        }

        .alert-footer {
            padding: 20px 30px;
            background: rgba(248, 249, 250, 0.8);
            border: none;
            display: flex;
            justify-content: center;
        }

        .alert-footer .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .alert-footer .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* 动画效果 */
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        /* 成功动画 */
        .success-animation {
            margin: 20px 0;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            margin: 0 auto;
        }

        .checkmark__svg {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #28a745;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #28a745;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 2;
            stroke-miterlimit: 10;
            stroke: #28a745;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {
            0%, 100% {
                transform: none;
            }
            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 30px #28a745;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-user-plus"></i>
                    分配问题
                </h1>
                <div class="subtitle">
                    当前用户: {{ user.chinese_name or user.username }} ({{ user.role }})
                </div>
            </div>
            <div class="header-actions">
                <a href="/bug/{{ bug.id }}" class="btn-modern btn-primary-modern">
                    <i class="fas fa-arrow-left"></i>
                    返回详情
                </a>
                <a href="/logout" class="btn-modern btn-danger-modern">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 问题信息 -->
            <div class="content-card">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    问题信息
                </div>

                <!-- 问题标题和状态 -->
                <div class="bug-title">
                    <span>#{{ bug.id }} - {{ bug.title }}</span>
                    <span class="badge status-{{ bug.status.lower().replace(' ', '-') }}">
                        <i class="fas fa-circle"></i>
                        {{ bug.status }}
                    </span>
                </div>

                <!-- 问题元信息 -->
                <div class="bug-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <span>提交人: {{ bug.creator_name }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar-plus"></i>
                        <span>创建时间: {{ bug.created_at }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-project-diagram"></i>
                        <span>项目: {{ bug.project or '未指定' }}</span>
                    </div>
                    {% if bug.assignee %}
                    <div class="meta-item">
                        <i class="fas fa-user-check"></i>
                        <span>当前负责人: {{ bug.assignee }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- 问题描述 -->
                <div class="section-title">
                    <i class="fas fa-file-alt"></i>
                    问题描述
                </div>
                <div class="bug-description">
                    {{ bug.description|replace('\n', '<br>')|safe }}
                </div>
            </div>

            <!-- 分配操作 -->
            {% if user.role_en == 'fzr' %}
            <div class="content-card">
                <div class="section-title">
                    <i class="fas fa-user-cog"></i>
                    分配操作
                </div>

                <form id="assignForm" action="/bug/assign/{{ bug.id }}" method="POST">
                    <div class="form-group">
                        <label for="assigned_to" class="form-label">
                            <i class="fas fa-users"></i>
                            选择团队成员
                        </label>
                        <select class="form-select" id="assigned_to" name="assigned_to" required>
                            <option value="">请选择要分配的团队成员...</option>
                            {% for member in team_members %}
                            <option value="{{ member.id }}">
                                {{ member.chinese_name or member.username }}
                                {% if member.team %} ({{ member.team }}){% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="action-buttons">
                        <button type="button" onclick="assignBug()" class="btn-modern btn-success-modern">
                            <i class="fas fa-check"></i>
                            确认分配
                        </button>
                        <a href="/bug/{{ bug.id }}" class="btn-modern btn-secondary-modern">
                            <i class="fas fa-times"></i>
                            取消操作
                        </a>
                    </div>
                </form>
            </div>
            {% else %}
            <!-- 权限不足提示 -->
            <div class="content-card">
                <div class="permission-notice">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h4>权限不足</h4>
                    <p>只有负责人角色才能分配问题。</p>
                    <div style="margin-top: 20px;">
                        <a href="/bug/{{ bug.id }}" class="btn-modern btn-primary-modern">
                            <i class="fas fa-arrow-left"></i>
                            返回问题详情
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 现代化确认Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header confirm-header">
                    <div class="confirm-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
                </div>
                <div class="modal-body confirm-body">
                    <p class="confirm-message" id="confirmModalMessage">确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer confirm-footer">
                    <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="button" class="btn-modern btn-primary" id="confirmModalBtn">
                        <i class="fas fa-check me-2"></i>确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化成功Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header success-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5 class="modal-title" id="successModalTitle">操作成功</h5>
                </div>
                <div class="modal-body success-body">
                    <div class="success-animation">
                        <div class="checkmark">
                            <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                                <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                            </svg>
                        </div>
                    </div>
                    <p class="success-message" id="successModalMessage">操作已成功完成</p>
                </div>
                <div class="modal-footer success-footer">
                    <button type="button" class="btn-modern btn-primary" id="successModalBtn">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化警告Modal -->
    <div class="modal fade" id="alertModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header alert-header">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h5 class="modal-title" id="alertModalTitle">提示</h5>
                </div>
                <div class="modal-body alert-body">
                    <p class="alert-message" id="alertModalMessage">请注意相关信息</p>
                </div>
                <div class="modal-footer alert-footer">
                    <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // 分配问题函数
    function assignBug() {
        const form = document.getElementById('assignForm');
        const assignedTo = document.getElementById('assigned_to').value;

        if (!assignedTo) {
            showCustomAlert('请选择要分配的团队成员！', 'warning');
            return;
        }

        showConfirmModal('确认分配', '确定要将此问题分配给选中的团队成员吗？', function() {
            const formData = new FormData(form);

            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if(data.success) {
                    showSuccessModal('分配成功！', '问题已成功分配给团队成员', function() {
                        window.location.href = data.redirect || '/bug/{{ bug.id }}';
                    });
                } else {
                    showCustomAlert('分配失败: ' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showCustomAlert('分配请求失败: ' + error.message, 'error');
            });
        });
    }

    // 表单验证
    document.getElementById('assigned_to').addEventListener('change', function() {
        const submitBtn = document.querySelector('.btn-success-modern');
        if (this.value) {
            submitBtn.style.opacity = '1';
            submitBtn.style.pointerEvents = 'auto';
        } else {
            submitBtn.style.opacity = '0.6';
            submitBtn.style.pointerEvents = 'none';
        }
    });

    // 初始化按钮状态
    document.addEventListener('DOMContentLoaded', function() {
        const assignedTo = document.getElementById('assigned_to');
        const submitBtn = document.querySelector('.btn-success-modern');
        if (assignedTo && submitBtn) {
            if (!assignedTo.value) {
                submitBtn.style.opacity = '0.6';
                submitBtn.style.pointerEvents = 'none';
            }
        }
    });

    // 现代化Modal函数
    function showConfirmModal(title, message, onConfirm) {
        document.getElementById('confirmModalTitle').textContent = title;
        document.getElementById('confirmModalMessage').textContent = message;

        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        modal.show();

        // 绑定确认按钮事件
        document.getElementById('confirmModalBtn').onclick = function() {
            modal.hide();
            if (onConfirm) onConfirm();
        };
    }

    function showSuccessModal(title, message, onClose) {
        document.getElementById('successModalTitle').textContent = title;
        document.getElementById('successModalMessage').textContent = message;

        const modal = new bootstrap.Modal(document.getElementById('successModal'));
        modal.show();

        // 绑定确定按钮事件
        document.getElementById('successModalBtn').onclick = function() {
            modal.hide();
            if (onClose) onClose();
        };
    }

    function showCustomAlert(message, type = 'warning') {
        const modal = new bootstrap.Modal(document.getElementById('alertModal'));
        const header = document.querySelector('.alert-header');
        const icon = document.querySelector('.alert-icon i');

        // 根据类型设置不同的样式
        if (type === 'error') {
            header.style.background = 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)';
            icon.className = 'fas fa-exclamation-circle';
            document.getElementById('alertModalTitle').textContent = '错误';
        } else if (type === 'warning') {
            header.style.background = 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)';
            icon.className = 'fas fa-exclamation-triangle';
            document.getElementById('alertModalTitle').textContent = '警告';
        } else {
            header.style.background = 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)';
            icon.className = 'fas fa-info-circle';
            document.getElementById('alertModalTitle').textContent = '提示';
        }

        document.getElementById('alertModalMessage').textContent = message;
        modal.show();
    }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
