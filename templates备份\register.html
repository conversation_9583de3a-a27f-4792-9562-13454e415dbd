<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - ReBugTracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 0;
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .register-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .register-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .register-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .register-content {
            display: flex;
            min-height: 600px;
        }
        
        .register-form {
            flex: 1;
            padding: 40px;
        }
        
        .register-info {
            flex: 0 0 300px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            animation: fadeInRight 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        .info-item:nth-child(1) { animation-delay: 0.1s; }
        .info-item:nth-child(2) { animation-delay: 0.2s; }
        .info-item:nth-child(3) { animation-delay: 0.3s; }
        .info-item:nth-child(4) { animation-delay: 0.4s; }
        
        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .info-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        
        .info-text h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .info-text p {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .form-section {
            margin-bottom: 30px;
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        .form-section:nth-child(1) { animation-delay: 0.1s; }
        .form-section:nth-child(2) { animation-delay: 0.2s; }
        .form-section:nth-child(3) { animation-delay: 0.3s; }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #667eea;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: translateY(-2px);
        }
        
        .form-label {
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .form-label i {
            margin-right: 8px;
            color: #667eea;
            width: 16px;
        }
        
        .required {
            color: #dc3545;
            margin-left: 5px;
        }
        
        .notification-preferences {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        
        .notification-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .notification-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .notification-item input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            accent-color: #667eea;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.1rem;
            color: white;
        }
        
        .email-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .gotify-icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .inapp-icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        
        .notification-text h5 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .notification-text p {
            font-size: 0.85rem;
            color: #666;
            margin: 0;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-register:hover::before {
            left: 100%;
        }
        
        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-link:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .register-content {
                flex-direction: column;
            }
            
            .register-info {
                flex: none;
                order: -1;
            }
            
            .register-header h1 {
                font-size: 2rem;
            }
            
            .register-form,
            .register-info {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <!-- 头部 -->
        <div class="register-header">
            <h1><i class="fas fa-user-plus"></i> 用户注册</h1>
            <p>加入 ReBugTracker，开始高效的问题管理之旅</p>
        </div>

        <!-- 主要内容 -->
        <div class="register-content">
            <!-- 注册表单 -->
            <div class="register-form">
                <form id="registerForm">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-user"></i>
                            基本信息
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user"></i>
                                        用户名
                                        <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" required
                                           placeholder="请输入用户名">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-id-card"></i>
                                        中文姓名
                                    </label>
                                    <input type="text" class="form-control" id="chinese_name" name="chinese_name"
                                           placeholder="请输入中文姓名">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-lock"></i>
                                密码
                                <span class="required">*</span>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required
                                   placeholder="请输入密码">
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-address-book"></i>
                            联系信息
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        邮箱地址
                                        <span class="required">*</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           placeholder="用于接收通知邮件">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-phone"></i>
                                        手机号码
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           placeholder="用于接收短信通知（可选）">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工作信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-briefcase"></i>
                            工作信息
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user-tag"></i>
                                        角色
                                        <span class="required">*</span>
                                    </label>
                                    <select class="form-control" id="role" name="role" required>
                                        <option value="">请选择角色</option>
                                        <option value="ssz">实施组人员</option>
                                        <option value="fzr">负责人</option>
                                        <option value="zncy">组内成员</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-building"></i>
                                        产品线
                                    </label>
                                    <input type="text" class="form-control" id="team" name="team"
                                           placeholder="请输入产品线">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通知偏好 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-bell"></i>
                            通知偏好设置
                        </div>

                        <div class="notification-preferences">
                            <div class="notification-item">
                                <input type="checkbox" name="email_notifications" id="email_notifications" checked>
                                <div class="notification-icon email-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="notification-text">
                                    <h5>邮件通知</h5>
                                    <p>通过邮件接收问题状态更新和重要通知</p>
                                </div>
                            </div>

                            <div class="notification-item">
                                <input type="checkbox" name="gotify_notifications" id="gotify_notifications" checked>
                                <div class="notification-icon gotify-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="notification-text">
                                    <h5>Gotify 通知</h5>
                                    <p>通过 Gotify 应用接收实时推送通知</p>
                                </div>
                            </div>

                            <div class="notification-item">
                                <input type="checkbox" name="inapp_notifications" id="inapp_notifications" checked>
                                <div class="notification-icon inapp-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="notification-text">
                                    <h5>应用内通知</h5>
                                    <p>在系统内直接显示通知消息</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-section">
                        <button type="submit" class="btn-register">
                            <i class="fas fa-user-plus"></i>
                            立即注册
                        </button>

                        <div class="text-center mt-3">
                            <span>已有账户？</span>
                            <a href="/login" class="btn-link">立即登录</a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 信息侧边栏 -->
            <div class="register-info">
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="info-text">
                        <h4>安全可靠</h4>
                        <p>采用先进的加密技术保护您的数据安全</p>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="info-text">
                        <h4>团队协作</h4>
                        <p>支持多角色协作，提高团队工作效率</p>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="info-text">
                        <h4>智能通知</h4>
                        <p>多渠道通知系统，确保重要信息不遗漏</p>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="info-text">
                        <h4>数据分析</h4>
                        <p>详细的统计报表，助力决策分析</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单提交处理
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.querySelector('.btn-register');
            const originalText = submitBtn.innerHTML;

            // 显示加载状态
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            submitBtn.disabled = true;

            try {
                const formData = new FormData(this);

                const response = await fetch('/register', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 成功提示
                    showNotification('注册成功！正在跳转到登录页面...', 'success');

                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    // 错误提示
                    showNotification(result.message || '注册失败，请重试', 'error');

                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            } catch (error) {
                console.error('注册错误:', error);
                showNotification('网络错误，请重试', 'error');

                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // 通知显示函数
        function showNotification(message, type) {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 500;
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                max-width: 300px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            `;

            if (type === 'success') {
                notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            } else {
                notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
            }

            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);

        // 表单验证增强
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.style.borderColor = '#dc3545';
                } else if (this.type === 'email' && this.value && !isValidEmail(this.value)) {
                    this.style.borderColor = '#dc3545';
                } else {
                    this.style.borderColor = '#28a745';
                }
            });

            input.addEventListener('focus', function() {
                this.style.borderColor = '#667eea';
            });
        });

        // 邮箱验证函数
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // 通知项点击切换
        document.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', function() {
                const checkbox = this.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            });
        });
    </script>
</body>
</html>
