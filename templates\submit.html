{% extends "base.html" %}

{% block content %}
<style>
    .submit-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 80px);
        padding: 20px 0;
    }

    .submit-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .submit-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .header-top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-left {
        flex: 1;
    }

    .header-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
    }

    .user-info-row {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #fff;
        font-size: 14px;
    }

    .user-info-row .user-name {
        font-weight: 600;
        font-size: 16px;
    }

    .user-info-row .user-role {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.9;
    }

    .user-info-row .user-team {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.8;
        font-size: 13px;
    }

    .header-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 300;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .btn-modern {
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(245, 87, 108, 0.4);
        color: white;
    }
</style>

<div class="submit-container">
    <div class="container">
        <!-- 现代化头部 -->
        <div class="submit-header">
            <div class="header-content">
                <div class="header-top-row">
                    <div class="header-left">
                        <h1 class="header-title">
                            <i class="fas fa-plus-circle me-3"></i>提交新问题需求
                        </h1>
                        <p class="header-subtitle">
                            <i class="fas fa-info-circle me-2"></i>请详细描述您遇到的问题或需求，我们将尽快为您处理
                        </p>
                    </div>
                    <div class="header-right">
                        <div class="user-info-row">
                            <span class="user-name">
                                <i class="fas fa-user me-2"></i>{{ session.chinese_name or session.username }}
                            </span>
                            <span class="user-role">
                                <i class="fas fa-id-badge me-1"></i>{{ session.role }}
                            </span>
                            {% if session.team %}
                            <span class="user-team">
                                <i class="fas fa-users me-1"></i>{{ session.team }}
                            </span>
                            {% endif %}
                        </div>
                        <div class="action-buttons">
                            <a href="/" class="btn-modern btn-primary">
                                <i class="fas fa-home"></i>返回首页
                            </a>
                            <a href="/logout" class="btn-modern btn-secondary">
                                <i class="fas fa-sign-out-alt"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if request.args.get('message') %}
        <div class="alert alert-success modern-alert" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: none; border-radius: 15px; padding: 20px; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);">
            <i class="fas fa-check-circle me-2"></i>{{ request.args.get('message') }}
        </div>
        {% endif %}

        <!-- 现代化表单卡片 -->
        <div class="form-card">
            <div class="form-header">
                <h3 class="form-title">
                    <i class="fas fa-edit me-2"></i>问题详情填写
                </h3>
                <p class="form-subtitle">请认真填写以下信息，以便我们更好地为您服务</p>
            </div>

            <form id="bugForm" method="POST" enctype="multipart/form-data">
                <!-- 项目信息区域 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-project-diagram me-2"></i>项目信息
                    </h4>

                    <div class="form-group">
                        <label for="project" class="form-label">
                            <i class="fas fa-folder me-2"></i>所属项目<span class="required">*</span>
                        </label>
                        <input type="text" id="project" name="project" class="form-control-modern"
                               placeholder="请输入项目名称，如：大连新一代项目、合肥D5000项目等" required>
                        <div class="validation-message"></div>
                    </div>

                    <div class="form-group">
                        <label for="manager" class="form-label">
                            <i class="fas fa-user-tie me-2"></i>推送给负责人<span class="required">*</span>
                        </label>
                        <select id="manager" name="manager" class="form-control-modern" required>
                            <option value="">请选择负责人</option>
                            {% for manager in managers %}
                            <option value="{{ manager }}">{{ manager }}</option>
                            {% endfor %}
                        </select>
                        <div class="validation-message"></div>
                    </div>
                </div>

                <!-- 问题描述区域 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-clipboard-list me-2"></i>问题描述
                    </h4>

                    <div class="form-group">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>需求/Bug标题<span class="required">*</span>
                        </label>
                        <input type="text" id="title" name="title" class="form-control-modern"
                               placeholder="请简洁明了地描述问题，如：登录页面无法正常跳转" required>
                        <div class="validation-message"></div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>详细描述<span class="required">*</span>
                        </label>
                        <textarea id="description" name="description" rows="6" class="form-control-modern"
                                  placeholder="请详细描述问题的具体情况：&#10;1. 问题出现的具体场景&#10;2. 预期的正确行为&#10;3. 实际发生的错误行为&#10;4. 问题的影响范围&#10;5. 其他相关信息" required></textarea>
                        <div class="validation-message"></div>
                    </div>
                </div>

                <!-- 附件上传区域 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-paperclip me-2"></i>附件上传
                    </h4>

                    <div class="form-group file-upload-modern">
                        <label for="images" class="form-label">
                            <i class="fas fa-image me-2"></i>问题截图（可选，支持多张）
                        </label>
                        <div class="file-upload-area" onclick="document.getElementById('images').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <p class="upload-title">点击上传截图</p>
                                <p class="upload-subtitle">支持 JPG、PNG、GIF 格式，最大 10MB，可选择多张图片</p>
                            </div>
                            <input type="file" id="images" name="images" accept="image/*" multiple style="display: none;">
                        </div>
                        <div class="images-preview" id="preview"></div>
                    </div>
                </div>

                <!-- 表单操作区域 -->
                <div class="form-actions-modern">
                    <button type="submit" class="btn-submit-modern">
                        <i class="fas fa-paper-plane me-2"></i>
                        <span class="submit-text">提交问题</span>
                        <div class="loading-spinner"></div>
                    </button>
                    <a href="/" class="btn-cancel-modern">
                        <i class="fas fa-times me-2"></i>取消返回
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 现代化成功提示Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header success-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h5 class="modal-title">提交成功！</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body success-body">
                <div class="success-animation">
                    <div class="checkmark">
                        <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                        </svg>
                    </div>
                </div>
                <p class="success-message">您的问题已成功提交，系统将尽快处理。</p>
                <p class="success-submessage">我们会在第一时间通知相关负责人，请耐心等待处理结果。</p>
            </div>
            <div class="modal-footer success-footer">
                <a href="/" class="btn-modern btn-primary me-2">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                <a href="/submit" class="btn-modern btn-success">
                    <i class="fas fa-plus me-2"></i>继续提交
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* 表单卡片样式 */
    .form-card {
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        border: 1px solid rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
    }

    .form-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f8f9fa;
    }

    .form-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .form-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
    }

    .form-section {
        margin-bottom: 40px;
        padding: 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        border-left: 4px solid #667eea;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .form-group {
        margin-bottom: 25px;
        position: relative;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .form-label .required {
        color: #dc3545;
        margin-left: 5px;
        font-weight: 700;
    }

    .form-control-modern {
        width: 100%;
        padding: 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
        transform: translateY(-2px);
    }

    .form-control-modern:invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .validation-message {
        color: #dc3545;
        font-size: 13px;
        margin-top: 8px;
        height: 20px;
        display: flex;
        align-items: center;
        font-weight: 500;
    }

    /* 文件上传样式 */
    .file-upload-modern {
        margin-bottom: 25px;
    }

    .file-upload-area {
        border: 3px dashed #dee2e6;
        padding: 40px 20px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .file-upload-area:hover {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .upload-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .file-upload-area:hover .upload-icon {
        color: white;
    }

    .upload-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
        transition: color 0.3s ease;
    }

    .file-upload-area:hover .upload-title {
        color: white;
    }

    .upload-subtitle {
        font-size: 0.9rem;
        color: #6c757d;
        margin: 0;
        transition: color 0.3s ease;
    }

    .file-upload-area:hover .upload-subtitle {
        color: rgba(255,255,255,0.8);
    }

    .image-preview img {
        max-width: 100%;
        max-height: 300px;
        border-radius: 12px;
        border: 3px solid #e9ecef;
        margin-top: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .image-preview img:hover {
        transform: scale(1.02);
    }

    /* 表单操作按钮 */
    .form-actions-modern {
        display: flex;
        gap: 15px;
        margin-top: 40px;
        justify-content: center;
        padding-top: 30px;
        border-top: 2px solid #f8f9fa;
    }

    .btn-submit-modern {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 5px 20px rgba(40, 167, 69, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-submit-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(40, 167, 69, 0.4);
    }

    .btn-submit-modern:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    .btn-cancel-modern {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        text-decoration: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 5px 20px rgba(108, 117, 125, 0.3);
    }

    .btn-cancel-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
    }

    .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255,255,255,0.3);
        border-top-color: white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        display: none;
        margin-left: 10px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }

    /* Modal样式 */
    .modern-modal {
        border: none;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .success-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 30px;
        text-align: center;
        position: relative;
    }

    .success-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        animation: bounceIn 0.6s ease-out;
    }

    @keyframes bounceIn {
        0% { transform: scale(0.3); opacity: 0; }
        50% { transform: scale(1.05); }
        70% { transform: scale(0.9); }
        100% { transform: scale(1); opacity: 1; }
    }

    .success-body {
        padding: 40px 30px;
        text-align: center;
        background: white;
    }

    .success-animation {
        margin-bottom: 25px;
    }

    .checkmark {
        width: 80px;
        height: 80px;
        margin: 0 auto;
    }

    .checkmark__svg {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #28a745;
        stroke-miterlimit: 10;
        animation: checkmark-fill 0.4s ease-in-out 0.4s forwards, checkmark-scale 0.3s ease-in-out 0.9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 2;
        stroke-miterlimit: 10;
        stroke: #28a745;
        fill: none;
        animation: checkmark-stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: checkmark-stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes checkmark-stroke {
        100% { stroke-dashoffset: 0; }
    }

    @keyframes checkmark-scale {
        0%, 100% { transform: none; }
        50% { transform: scale3d(1.1, 1.1, 1); }
    }

    @keyframes checkmark-fill {
        100% { box-shadow: inset 0px 0px 0px 30px #28a745; }
    }

    .success-message {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .success-submessage {
        color: #6c757d;
        margin-bottom: 0;
    }

    .success-footer {
        background: #f8f9fa;
        border: none;
        padding: 25px 30px;
        justify-content: center;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .submit-container {
            padding: 10px 0;
        }

        .submit-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .header-title {
            font-size: 1.8rem;
        }

        .header-top-row {
            flex-direction: column;
            text-align: center;
        }

        .header-right {
            align-items: center;
        }

        .user-info-row {
            flex-direction: column;
            gap: 8px;
        }

        .action-buttons {
            justify-content: center;
        }

        .form-card {
            padding: 25px 20px;
            margin: 0 10px 20px;
        }

        .form-section {
            padding: 20px 15px;
        }

        .form-actions-modern {
            flex-direction: column;
            align-items: center;
        }

        .btn-submit-modern,
        .btn-cancel-modern {
            width: 100%;
            justify-content: center;
            max-width: 300px;
        }

        .file-upload-area {
            padding: 30px 15px;
        }

        .upload-icon {
            font-size: 2.5rem;
        }
    }

    @media (max-width: 480px) {
        .header-title {
            font-size: 1.5rem;
        }

        .form-title {
            font-size: 1.4rem;
        }

        .section-title {
            font-size: 1rem;
        }

        .form-control-modern {
            padding: 12px 15px;
        }

        .btn-modern {
            padding: 10px 20px;
            font-size: 13px;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const imagesInput = document.getElementById('images');
    const previewDiv = document.getElementById('preview');
    const form = document.getElementById('bugForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const loadingSpinner = submitBtn.querySelector('.loading-spinner');
    const submitText = submitBtn.querySelector('.submit-text');
    const fileUploadArea = document.querySelector('.file-upload-area');

    // 存储已选择的文件
    let selectedFiles = [];

    // 文件拖拽上传功能
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#667eea';
        fileUploadArea.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        fileUploadArea.style.color = 'white';
    });

    fileUploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#dee2e6';
        fileUploadArea.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)';
        fileUploadArea.style.color = '';
    });

    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#dee2e6';
        fileUploadArea.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)';
        fileUploadArea.style.color = '';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            // 添加新文件到已选择的文件列表
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                }
            }
            updateFileInput();
            updatePreview();
        }
    });

    // 图片预览功能
    imagesInput.addEventListener('change', function() {
        if (this.files && this.files.length > 0) {
            // 添加新文件到已选择的文件列表
            for (let file of this.files) {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                }
            }
            updateFileInput();
            updatePreview();
        }
    });

    // 更新文件输入框
    function updateFileInput() {
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        imagesInput.files = dt.files;
    }

    // 更新预览显示
    function updatePreview() {
        if (selectedFiles.length === 0) {
            previewDiv.innerHTML = '';
            return;
        }

        let previewHTML = '<div class="images-preview-container" style="margin-top: 20px;">';
        previewHTML += `<div style="margin-bottom: 15px; color: #6c757d; font-size: 14px;">
                            <i class="fas fa-images me-1"></i>已选择 ${selectedFiles.length} 张图片
                        </div>`;
        previewHTML += '<div class="images-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px;">';

        selectedFiles.forEach((file, index) => {
            // 文件大小检查
            if (file.size > 10 * 1024 * 1024) {
                showNotification('文件大小不能超过10MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const imageContainer = document.getElementById(`image-container-${index}`);
                if (imageContainer) {
                    imageContainer.innerHTML = `
                        <div style="position: relative; border-radius: 8px; overflow: hidden; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                            <img src="${e.target.result}"
                                 alt="预览图 ${index + 1}"
                                 style="width: 100%; height: 120px; object-fit: cover; cursor: pointer;"
                                 onclick="this.style.transform = this.style.transform ? '' : 'scale(2)'; this.style.zIndex = this.style.zIndex ? '' : '1000'; this.style.position = this.style.position ? '' : 'fixed'; this.style.top = this.style.top ? '' : '50%'; this.style.left = this.style.left ? '' : '50%'; this.style.transform = this.style.transform ? '' : 'translate(-50%, -50%) scale(2)'; this.style.background = this.style.background ? '' : 'rgba(0,0,0,0.8)';">
                            <button type="button" class="remove-image-btn"
                                    style="position: absolute; top: 5px; right: 5px; background: #dc3545; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 5px rgba(0,0,0,0.3);"
                                    onclick="removeImage(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.7); color: white; padding: 5px; font-size: 12px; text-align: center;">
                                ${file.name.length > 15 ? file.name.substring(0, 12) + '...' : file.name}
                            </div>
                        </div>
                    `;
                }
            };
            reader.readAsDataURL(file);

            previewHTML += `<div id="image-container-${index}" style="min-height: 120px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-spinner fa-spin" style="color: #6c757d;"></i>
                            </div>`;
        });

        previewHTML += '</div>';
        previewHTML += `<div style="text-align: center; margin-top: 15px; color: #6c757d; font-size: 14px;">
                            <i class="fas fa-info-circle me-1"></i>点击图片可放大查看
                        </div>`;
        previewHTML += '</div>';
        previewDiv.innerHTML = previewHTML;
    }

    // 移除图片函数
    window.removeImage = function(index) {
        if (typeof index === 'number') {
            // 删除指定索引的图片
            selectedFiles.splice(index, 1);
            showNotification('图片已移除', 'success');
        } else {
            // 删除所有图片
            selectedFiles = [];
            showNotification('所有图片已移除', 'success');
        }
        updateFileInput();
        updatePreview();
    };

    // 表单提交处理
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // 表单验证
        if (!validateForm()) {
            return;
        }

        // 显示加载状态
        submitText.textContent = '提交中...';
        loadingSpinner.style.display = 'inline-block';
        submitBtn.disabled = true;

        // 添加提交动画
        submitBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            submitBtn.style.transform = '';
        }, 150);

        try {
            const formData = new FormData(form);

            // 移除所有图片字段，避免重复
            if (formData.has('images')) {
                formData.delete('images');
            }
            if (formData.has('image')) {
                formData.delete('image');
            }

            // 只添加selectedFiles中的图片
            if (selectedFiles.length > 0) {
                selectedFiles.forEach(file => {
                    formData.append('images', file);
                });
            }

            const response = await fetch('/bug/submit', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) throw new Error('Network error');
            const data = await response.json();

            if (data.success) {
                // 显示成功Modal
                const modal = new bootstrap.Modal(document.getElementById('successModal'));
                modal.show();

                // 重置表单
                form.reset();
                previewDiv.innerHTML = '';

                // 5秒后自动跳转
                setTimeout(() => {
                    window.location.href = data.redirect || '/';
                }, 5000);
            } else {
                showNotification(data.message || '提交失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('网络错误，请检查网络连接后重试', 'error');
        } finally {
            // 恢复按钮状态
            submitText.textContent = '提交问题';
            loadingSpinner.style.display = 'none';
            submitBtn.disabled = false;
        }
    });

    // 表单验证函数
    function validateForm() {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            const validationMsg = field.closest('.form-group').querySelector('.validation-message');

            if (!field.value.trim()) {
                validationMsg.textContent = '此字段为必填项';
                field.style.borderColor = '#dc3545';
                isValid = false;
            } else {
                validationMsg.textContent = '';
                field.style.borderColor = '#28a745';
            }
        });

        return isValid;
    }

    // 实时表单验证
    form.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('input', () => {
            const validationMsg = element.closest('.form-group').querySelector('.validation-message');

            if (element.validity.valid && element.value.trim()) {
                validationMsg.textContent = '';
                element.style.borderColor = '#28a745';
                element.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
            } else if (element.value.trim() === '' && element.hasAttribute('required')) {
                validationMsg.textContent = '此字段为必填项';
                element.style.borderColor = '#dc3545';
                element.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
            } else {
                validationMsg.textContent = '';
                element.style.borderColor = '#667eea';
                element.style.boxShadow = '0 0 0 0.2rem rgba(102, 126, 234, 0.25)';
            }
        });

        element.addEventListener('blur', () => {
            if (element.hasAttribute('required') && !element.value.trim()) {
                const validationMsg = element.closest('.form-group').querySelector('.validation-message');
                validationMsg.textContent = '此字段为必填项';
                element.style.borderColor = '#dc3545';
            }
        });
    });

    // 通知函数
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            animation: slideInRight 0.3s ease-out;
        `;

        const icon = type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'success' ? 'fas fa-check-circle' : 'fas fa-info-circle';

        notification.innerHTML = `
            <i class="${icon} me-2"></i>${message}
            <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 3000);
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
