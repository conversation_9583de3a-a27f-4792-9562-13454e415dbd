<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置 - ReBugTracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            border: none;
        }

        .card-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-modern {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary-modern:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary-modern {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-test {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-test:hover {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .alert-modern {
            border: none;
            border-radius: 15px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .help-text {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-configured {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-not-configured {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .back-link {
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .back-link:hover {
            color: #f8f9fa;
            text-decoration: none;
        }

        /* 现代化Modal样式 */
        .modern-modal {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        /* 成功Modal样式 */
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .success-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .success-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .success-animation {
            margin-bottom: 25px;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #28a745;
            stroke-miterlimit: 10;
            margin: 0 auto 20px;
            box-shadow: inset 0px 0px 0px #28a745;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 2;
            stroke-miterlimit: 10;
            stroke: #28a745;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        .success-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .success-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            text-align: center;
        }

        .success-footer .btn-modern {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .success-footer .btn-modern:hover {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        /* 错误Modal样式 */
        .error-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .error-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .error-body {
            padding: 40px 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .error-animation {
            margin-bottom: 25px;
        }

        .error-icon-large {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .error-message {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .error-footer {
            background: white;
            border: none;
            padding: 20px 30px;
            text-align: center;
        }

        .error-footer .btn-modern {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .error-footer .btn-modern:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        /* 动画效果 */
        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {
            0%, 100% {
                transform: none;
            }
            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 30px #28a745;
            }
        }

        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            10%, 30%, 50%, 70%, 90% {
                transform: translateX(-5px);
            }
            20%, 40%, 60%, 80% {
                transform: translateX(5px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>

        <div class="settings-card">
            <div class="card-header">
                <h4><i class="fas fa-bell me-2"></i>通知设置</h4>
            </div>
            <div class="card-body">
                <!-- 邮件设置 -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-envelope me-2"></i>邮件通知设置
                    </label>

                    <div class="mb-3">
                        <span class="status-indicator" id="emailStatus">
                            <i class="fas fa-times-circle"></i>
                            未配置
                        </span>
                    </div>

                    <div class="mb-3">
                        <label for="userEmail" class="form-label">邮箱地址</label>
                        <input type="email" class="form-control" id="userEmail"
                               placeholder="输入您的邮箱地址">
                        <div class="help-text">
                            用于接收问题状态变更等重要通知
                        </div>
                    </div>


                </div>

                <hr class="my-4">

                <!-- Gotify设置 -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-mobile-alt me-2"></i>Gotify推送设置
                    </label>
                    
                    <div class="mb-3">
                        <span class="status-indicator" id="gotifyStatus">
                            <i class="fas fa-times-circle"></i>
                            未配置
                        </span>
                    </div>

                    <div class="mb-3">
                        <label for="gotifyAppToken" class="form-label">Gotify App Token</label>
                        <input type="password" class="form-control" id="gotifyAppToken" 
                               placeholder="输入您的Gotify App Token">
                        <div class="help-text">
                            请在Gotify服务器中创建个人应用，然后将App Token粘贴到这里
                        </div>
                    </div>


                </div>

                <!-- 通知偏好设置 -->
                <hr class="my-4">
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-sliders-h me-2"></i>通知偏好
                    </label>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="enableInAppNotifications" checked>
                        <label class="form-check-label" for="enableInAppNotifications">
                            应用内通知
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="enableEmailNotifications">
                        <label class="form-check-label" for="enableEmailNotifications">
                            邮件通知
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="enableGotifyNotifications">
                        <label class="form-check-label" for="enableGotifyNotifications">
                            Gotify推送通知
                        </label>
                    </div>

                </div>

                <!-- 全部保存按钮 -->
                <div class="text-end mt-4">
                    <button type="button" class="btn btn-modern btn-primary-modern btn-lg" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i>
                        全部保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化成功提示Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header success-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5 class="modal-title" id="successModalTitle">操作成功！</h5>
                </div>
                <div class="modal-body success-body">
                    <div class="success-animation">
                        <div class="checkmark">
                            <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                                <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                            </svg>
                        </div>
                    </div>
                    <p class="success-message" id="successModalMessage">操作已成功完成</p>
                </div>
                <div class="modal-footer success-footer">
                    <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化错误提示Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header error-header">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h5 class="modal-title">操作失败</h5>
                </div>
                <div class="modal-body error-body">
                    <div class="error-animation">
                        <div class="error-icon-large">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                    <p class="error-message" id="errorModalMessage">操作失败，请稍后重试</p>
                </div>
                <div class="modal-footer error-footer">
                    <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载时获取当前设置
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSettings();
        });

        function loadCurrentSettings() {
            // 加载邮件设置
            fetch('/user/email-settings')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.email) {
                            document.getElementById('userEmail').value = data.email;
                            updateEmailStatus(true);
                        } else {
                            updateEmailStatus(false);
                        }
                    }
                })
                .catch(error => console.error('Error loading email settings:', error));

            // 加载Gotify设置
            fetch('/user/gotify-settings')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.app_token) {
                            document.getElementById('gotifyAppToken').value = data.app_token;
                            updateGotifyStatus(true);
                        } else {
                            updateGotifyStatus(false);
                        }
                    }
                })
                .catch(error => console.error('Error loading Gotify settings:', error));

            // 加载通知偏好
            fetch('/user/notification-preferences')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('enableInAppNotifications').checked = data.inapp_enabled;
                        document.getElementById('enableEmailNotifications').checked = data.email_enabled;
                        document.getElementById('enableGotifyNotifications').checked = data.gotify_enabled;
                    }
                })
                .catch(error => console.error('Error loading notification preferences:', error));
        }

        function updateEmailStatus(configured) {
            const statusElement = document.getElementById('emailStatus');
            if (configured) {
                statusElement.className = 'status-indicator status-configured';
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i> 已配置';
            } else {
                statusElement.className = 'status-indicator status-not-configured';
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i> 未配置';
            }
        }

        function updateGotifyStatus(configured) {
            const statusElement = document.getElementById('gotifyStatus');
            if (configured) {
                statusElement.className = 'status-indicator status-configured';
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i> 已配置';
            } else {
                statusElement.className = 'status-indicator status-not-configured';
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i> 未配置';
            }
        }

        function saveGotifySettings() {
            const appToken = document.getElementById('gotifyAppToken').value.trim();

            fetch('/user/gotify-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    app_token: appToken
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    updateGotifyStatus(appToken ? true : false);
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败，请重试');
            });
        }

        function testGotifyConnection() {
            const appToken = document.getElementById('gotifyAppToken').value.trim();
            
            if (!appToken) {
                alert('请先输入Gotify App Token');
                return;
            }

            fetch('/user/test-gotify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    app_token: appToken
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('测试成功！您应该收到了一条测试通知。');
                } else {
                    alert('测试失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('测试失败，请检查Token是否正确');
            });
        }

        function saveEmailSettings() {
            const email = document.getElementById('userEmail').value.trim();

            // 如果邮箱不为空，进行格式验证
            if (email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    alert('请输入有效的邮箱地址');
                    return;
                }
            }

            fetch('/user/email-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    updateEmailStatus(email ? true : false);
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败，请重试');
            });
        }

        function testEmailConnection() {
            const email = document.getElementById('userEmail').value.trim();

            if (!email) {
                alert('请先输入邮箱地址');
                return;
            }

            fetch('/user/test-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('测试邮件发送成功！请检查您的邮箱。');
                } else {
                    alert('测试失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('测试失败，请检查邮箱地址是否正确');
            });
        }

        function saveNotificationPreferences() {
            const preferences = {
                inapp_enabled: document.getElementById('enableInAppNotifications').checked,
                email_enabled: document.getElementById('enableEmailNotifications').checked,
                gotify_enabled: document.getElementById('enableGotifyNotifications').checked
            };

            fetch('/user/notification-preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(preferences)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('通知偏好保存成功！');
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败，请重试');
            });
        }
        // 新增的统一Modal函数
        function showSuccessModal(title, message) {
            document.getElementById('successModalTitle').textContent = title;
            document.getElementById('successModalMessage').textContent = message;
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
        }

        function showErrorModal(message) {
            document.getElementById('errorModalMessage').textContent = message;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }

        // 全部保存函数
        function saveAllSettings() {
            const email = document.getElementById('userEmail').value.trim();
            const appToken = document.getElementById('gotifyAppToken').value.trim();
            const preferences = {
                inapp_enabled: document.getElementById('enableInAppNotifications').checked,
                email_enabled: document.getElementById('enableEmailNotifications').checked,
                gotify_enabled: document.getElementById('enableGotifyNotifications').checked
            };

            let promises = [];
            let results = [];

            // 保存邮箱设置（允许空值）
            if (email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showErrorModal('请输入有效的邮箱地址');
                    return;
                }
            }

            // 总是发送邮箱设置请求，即使为空
            promises.push(
                fetch('/user/email-settings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: email })
                }).then(response => response.json())
                .then(data => {
                    results.push({ type: '邮箱设置', success: data.success, message: data.message });
                    if (data.success) {
                        updateEmailStatus(email ? true : false);
                    }
                })
            );

            // 保存Gotify设置（允许空值）
            promises.push(
                fetch('/user/gotify-settings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ app_token: appToken })
                }).then(response => response.json())
                .then(data => {
                    results.push({ type: 'Gotify设置', success: data.success, message: data.message });
                    if (data.success) {
                        updateGotifyStatus(appToken ? true : false);
                    }
                })
            );

            // 保存通知偏好
            promises.push(
                fetch('/user/notification-preferences', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(preferences)
                }).then(response => response.json())
                .then(data => {
                    results.push({ type: '通知偏好', success: data.success, message: data.message });
                })
            );

            // 等待所有请求完成
            Promise.all(promises)
                .then(() => {
                    const failedItems = results.filter(r => !r.success);
                    if (failedItems.length === 0) {
                        showSuccessModal('保存成功！', '所有设置已成功保存');
                    } else {
                        const failedMessages = failedItems.map(item => `${item.type}: ${item.message}`).join('\\n');
                        showErrorModal('部分设置保存失败:\\n' + failedMessages);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showErrorModal('保存失败，请检查网络连接后重试');
                });
        }
    </script>
</body>
</html>
