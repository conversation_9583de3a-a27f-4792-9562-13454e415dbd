<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知管理 - ReBugTracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .notification-card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .server-status {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .server-status.enabled {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .server-status.disabled {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }
        .user-row {
            transition: all 0.3s ease;
        }
        .user-row:hover {
            background-color: #f8f9fa;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .role-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-bug"></i> ReBugTracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home"></i> 首页
                </a>
                <a class="nav-link" href="/logout">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-bell"></i> 通知系统管理</h2>
                <p class="text-muted">管理服务器通知功能和用户通知偏好设置</p>
            </div>
        </div>

        <!-- 服务器通知状态 -->
        <div class="row">
            <div class="col-12">
                <div class="server-status {% if server_enabled %}enabled{% else %}disabled{% endif %}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4><i class="fas fa-server"></i> 服务器通知功能</h4>
                            <p class="mb-0">
                                {% if server_enabled %}
                                    <i class="fas fa-check-circle"></i> 通知功能已启用
                                {% else %}
                                    <i class="fas fa-times-circle"></i> 通知功能已禁用
                                {% endif %}
                            </p>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="serverToggle" {% if server_enabled %}checked{% endif %}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户通知设置 -->
        <div class="row">
            <div class="col-12">
                <div class="notification-card">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-users"></i> 用户通知设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>用户</th>
                                            <th>角色</th>
                                            <th><i class="fas fa-envelope"></i> 邮件</th>
                                            <th><i class="fas fa-mobile-alt"></i> Gotify</th>
                                            <th><i class="fas fa-bell"></i> 应用内</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in users %}
                                        <tr class="user-row" data-user-id="{{ user[0] }}">
                                            <td>
                                                <div>
                                                    <strong>{{ user[2] or user[1] }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ user[1] }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                {% set role_map = {'gly': '管理员', 'fzr': '负责人', 'ssz': '实施组', 'zncy': '组内成员'} %}
                                                {% set role_color = {'gly': 'danger', 'fzr': 'warning', 'ssz': 'info', 'zncy': 'success'} %}
                                                <span class="badge bg-{{ role_color.get(user[3], 'secondary') }} role-badge">
                                                    {{ role_map.get(user[3], user[3]) }}
                                                </span>
                                            </td>
                                            <td>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" class="channel-toggle" 
                                                           data-channel="email" 
                                                           {% if user[4] %}checked{% endif %}>
                                                    <span class="slider"></span>
                                                </label>
                                            </td>
                                            <td>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" class="channel-toggle" 
                                                           data-channel="gotify" 
                                                           {% if user[5] %}checked{% endif %}>
                                                    <span class="slider"></span>
                                                </label>
                                            </td>
                                            <td>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" class="channel-toggle" 
                                                           data-channel="inapp" 
                                                           {% if user[6] %}checked{% endif %}>
                                                    <span class="slider"></span>
                                                </label>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知统计 -->
        <div class="row">
            <div class="col-md-4">
                <div class="card notification-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x text-primary mb-3"></i>
                        <h5>总用户数</h5>
                        <h3 class="text-primary">{{ users|length }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card notification-card">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-2x text-success mb-3"></i>
                        <h5>邮件通知启用</h5>
                        <h3 class="text-success">{{ users|selectattr('4')|list|length }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card notification-card">
                    <div class="card-body text-center">
                        <i class="fas fa-mobile-alt fa-2x text-info mb-3"></i>
                        <h5>Gotify通知启用</h5>
                        <h3 class="text-info">{{ users|selectattr('5')|list|length }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 服务器通知开关
        document.getElementById('serverToggle').addEventListener('change', function() {
            const enabled = this.checked;
            
            fetch('/admin/notifications/server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ enabled: enabled })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新UI
                    const statusDiv = document.querySelector('.server-status');
                    if (enabled) {
                        statusDiv.className = 'server-status enabled';
                        statusDiv.querySelector('p').innerHTML = '<i class="fas fa-check-circle"></i> 通知功能已启用';
                    } else {
                        statusDiv.className = 'server-status disabled';
                        statusDiv.querySelector('p').innerHTML = '<i class="fas fa-times-circle"></i> 通知功能已禁用';
                    }
                    
                    // 显示成功消息
                    showAlert('success', `服务器通知功能已${enabled ? '启用' : '禁用'}`);
                } else {
                    // 恢复开关状态
                    this.checked = !enabled;
                    showAlert('danger', data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.checked = !enabled;
                showAlert('danger', '网络错误，请重试');
            });
        });

        // 用户通知开关
        document.querySelectorAll('.channel-toggle').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const userId = this.closest('tr').dataset.userId;
                const channel = this.dataset.channel;
                const enabled = this.checked;
                
                fetch('/admin/notifications/user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        channel: channel,
                        enabled: enabled
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', `用户${channel}通知已${enabled ? '启用' : '禁用'}`);
                    } else {
                        // 恢复开关状态
                        this.checked = !enabled;
                        showAlert('danger', data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.checked = !enabled;
                    showAlert('danger', '网络错误，请重试');
                });
            });
        });

        // 显示提示消息
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
