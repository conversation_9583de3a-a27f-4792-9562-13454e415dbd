# ReBugTracker 通知优先级系统详解

## 📋 概述

ReBugTracker使用了一个**两层优先级系统**来管理通知的重要程度和显示效果：

1. **内部优先级**（1-4级）- 系统内部的业务逻辑优先级
2. **渠道优先级**（各渠道特定）- 转换后适配各通知渠道的优先级

## 🎯 内部优先级系统（1-4级）

### 优先级定义

| 内部优先级 | 重要程度 | 使用场景 | 颜色标识 |
|-----------|----------|----------|----------|
| **1** | 🔵 **普通** | 状态更新、问题关闭 | 蓝色/绿色 |
| **2** | 🟡 **重要** | 问题创建、问题解决 | 黄色 |
| **3** | 🟠 **紧急** | 问题分配 | 橙色 |
| **4** | 🔴 **严重** | 系统错误、关键问题 | 红色 |

### 具体事件的优先级分配

```python
# 当前系统中的事件优先级设置
templates = {
    "bug_created": {
        "priority": 2  # 重要 - 新问题需要及时处理
    },
    "bug_assigned": {
        "priority": 3  # 紧急 - 直接分配给用户，需要立即关注
    },
    "bug_status_changed": {
        "priority": 1  # 普通 - 状态更新，信息性通知
    },
    "bug_resolved": {
        "priority": 2  # 重要 - 问题解决，需要确认
    },
    "bug_closed": {
        "priority": 1  # 普通 - 问题关闭，流程结束
    }
}
```

## 🔄 渠道优先级转换

### 1. 📧 邮件通知
- **用途**：邮件模板的颜色主题
- **转换**：直接使用内部优先级
- **效果**：
  - 优先级1 → 绿色主题 `#28a745`
  - 优先级2 → 黄色主题 `#ffc107`
  - 优先级3 → 橙色主题 `#fd7e14`
  - 优先级4 → 红色主题 `#dc3545`

### 2. 🔔 Gotify推送通知
- **用途**：Gotify客户端的通知优先级
- **转换**：**固定为10**（最高优先级）
- **原因**：确保所有ReBugTracker通知都能及时到达用户

```python
# 修改前的转换逻辑
gotify_priority = min(max(priority * 2, 1), 10)
# 内部1 → Gotify2, 内部2 → Gotify4, 内部3 → Gotify6, 内部4 → Gotify8

# 修改后的转换逻辑
gotify_priority = 10  # 所有通知都使用最高优先级
```

### 3. 📱 应用内通知
- **用途**：应用内通知中心的显示
- **转换**：直接使用内部优先级
- **效果**：影响通知在应用内的排序和显示样式

## 🎨 视觉效果示例

### 邮件通知的视觉差异

#### 优先级1（普通）- 绿色主题
```
┌─────────────────────────────────┐
│ 🔄 问题状态更新 [绿色背景]        │
├─────────────────────────────────┤
│ 问题状态已更新：                 │
│ 📋 标题：网络连接问题            │
│ 📊 状态：处理中 → 已解决         │
└─────────────────────────────────┘
```

#### 优先级2（重要）- 黄色主题
```
┌─────────────────────────────────┐
│ 🆕 有新的提交问题 [黄色背景]      │
├─────────────────────────────────┤
│ 张三提交了新问题，请及时处理：    │
│ 📋 标题：服务器宕机              │
│ 📝 描述：生产服务器无法访问      │
└─────────────────────────────────┘
```

#### 优先级3（紧急）- 橙色主题
```
┌─────────────────────────────────┐
│ 🔔 问题分配给您 [橙色背景]        │
├─────────────────────────────────┤
│ 您好 李四，有问题分配给您：       │
│ 📋 标题：数据库连接异常          │
│ 👤 分配人：王五                  │
└─────────────────────────────────┘
```

## 🔧 设计理念

### 为什么使用两层优先级？

1. **业务逻辑分离**
   - 内部优先级专注于业务重要性
   - 渠道优先级适配不同通知平台

2. **灵活性**
   - 可以独立调整各渠道的优先级策略
   - 不影响其他渠道的通知效果

3. **可维护性**
   - 业务逻辑变更只需修改内部优先级
   - 渠道适配逻辑独立维护

### 当前的Gotify策略

**为什么所有Gotify通知都设为优先级10？**

1. **重要性**：ReBugTracker的所有通知都是工作相关的重要信息
2. **及时性**：问题处理需要快速响应，不能被其他低优先级通知掩盖
3. **简化**：避免用户因为优先级设置错过重要通知

## 📊 优先级使用统计

| 事件类型 | 内部优先级 | 频率 | 重要程度 |
|---------|-----------|------|----------|
| 问题分配 | 3 | 高 | 需要立即处理 |
| 问题创建 | 2 | 中 | 需要及时关注 |
| 问题解决 | 2 | 中 | 需要确认验证 |
| 状态更新 | 1 | 高 | 信息性通知 |
| 问题关闭 | 1 | 低 | 流程完成 |

## 🛠️ 自定义优先级

如果需要调整优先级，可以修改 `notification/simple_notifier.py` 中的模板配置：

```python
"bug_created": {
    "title": "🆕 有新的提交问题",
    "content": "...",
    "priority": 2  # 修改这里的数值
},
```

**注意**：修改后需要重启应用才能生效。

---

**总结**：内部优先级系统为ReBugTracker提供了灵活的通知管理机制，既保证了业务逻辑的清晰性，又适配了不同通知渠道的特性。
