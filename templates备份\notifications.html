{% extends "base.html" %}

{% block content %}
<style>
    .dashboard-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 80px);
        padding: 20px 0;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-modern {
        padding: 10px 20px;
        border-radius: 25px;
        border: none;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-secondary-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }

    .btn-secondary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(168, 237, 234, 0.4);
        color: #333;
    }

    .btn-home-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-home-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .notifications-section {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .notifications-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notifications-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .notifications-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 500;
    }
</style>

<div class="dashboard-container">
    <div class="container">
        <!-- 页面头部 -->
        <div class="dashboard-header">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="fas fa-bell"></i>
                    通知中心
                </h1>
                <div class="header-actions">
                    <a href="/" class="btn-modern btn-home-modern">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <button class="btn-modern btn-primary-modern" onclick="markAllAsRead()">
                        <i class="fas fa-check-double"></i> 全部标记为已读
                    </button>
                    <button class="btn-modern btn-secondary-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 通知列表 -->
        <div class="notifications-section">
            <div class="notifications-header">
                <h3 class="notifications-title">
                    <i class="fas fa-list"></i>
                    通知列表
                </h3>
                <div class="notifications-count">
                    共 {{ notifications|length if notifications else 0 }} 条通知
                </div>
            </div>

            <!-- 通知内容 -->
            {% if notifications %}
            <div class="notifications-content">
                {% for notification in notifications %}
                <div class="notification-item {% if not notification.read_status %}unread{% endif %}"
                     data-notification-id="{{ notification.id }}"
                     onclick="markAsReadAndNavigate({{ notification.id }}, {{ notification.related_bug_id or 'null' }})">
                    <div class="notification-header">
                        <div class="notification-title-section">
                            <h5 class="notification-title">{{ notification.title }}</h5>
                            {% if not notification.read_status %}
                            <span class="notification-badge">未读</span>
                            {% endif %}
                        </div>
                        <div class="notification-status">
                            {% if not notification.read_status %}
                            <i class="fas fa-circle notification-unread-icon"></i>
                            {% else %}
                            <i class="fas fa-check-circle notification-read-icon"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="notification-content">
                        <p class="notification-text">{{ notification.content }}</p>
                    </div>
                    <div class="notification-meta">
                        <div class="notification-meta-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ notification.created_at }}</span>
                        </div>
                        {% if notification.related_bug_id %}
                        <div class="notification-meta-item">
                            <i class="fas fa-link"></i>
                            <span>关联问题 #{{ notification.related_bug_id }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <h5 class="empty-title">暂无通知</h5>
                <p class="empty-text">您目前没有任何通知消息</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* 通知项样式 - 适配index风格 */
    .notification-item {
        padding: 20px 25px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    /* 斑马纹效果 - 奇数行（明显的蓝色调） */
    .notification-item:nth-child(odd) {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-left: 6px solid #2196f3;
    }

    /* 斑马纹效果 - 偶数行（明显的绿色调） */
    .notification-item:nth-child(even) {
        background: linear-gradient(135deg, #f1f8e9, #dcedc8);
        border-left: 6px solid #4caf50;
    }

    .notification-item:hover {
        transform: translateX(10px);
    }

    /* 悬停时的特殊效果 */
    .notification-item:nth-child(odd):hover {
        background: linear-gradient(135deg, #bbdefb, #90caf9);
        border-left-color: #1976d2;
        box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
    }

    .notification-item:nth-child(even):hover {
        background: linear-gradient(135deg, #dcedc8, #c8e6c9);
        border-left-color: #388e3c;
        box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
    }

    /* 未读通知特殊样式 */
    .notification-item.unread {
        border-left-width: 8px;
        position: relative;
    }

    .notification-item.unread::before {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 12px;
        height: 12px;
        background: #ff4444;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.7;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        gap: 15px;
    }

    .notification-title-section {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .notification-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .notification-badge {
        background: #667eea;
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .notification-status {
        display: flex;
        align-items: center;
    }

    .notification-unread-icon {
        color: #ff4444;
        font-size: 12px;
        animation: pulse 2s infinite;
    }

    .notification-read-icon {
        color: #4caf50;
        font-size: 16px;
    }

    .notification-content {
        margin-bottom: 15px;
    }

    .notification-text {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
        margin: 0;
        text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
    }

    .notification-meta {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .notification-meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #666;
        font-size: 0.9rem;
    }

    .notification-meta-item i {
        color: #999;
    }

    /* 空状态样式 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-icon {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-title {
        color: #999;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .empty-text {
        color: #bbb;
        font-size: 1rem;
        margin: 0;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .header-title {
            font-size: 2rem;
        }

        .notification-item {
            padding: 15px 20px;
        }

        .notification-header {
            flex-direction: column;
            gap: 10px;
        }

        .notification-title-section {
            justify-content: center;
        }

        .notification-meta {
            justify-content: center;
            gap: 15px;
        }
    }
</style>

<script>
// 标记通知为已读并导航
async function markAsReadAndNavigate(notificationId, bugId) {
    try {
        await fetch('/api/notifications/read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notification_id: notificationId })
        });
        
        // 更新界面状态
        const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (notificationElement) {
            notificationElement.classList.remove('unread');
            const badge = notificationElement.querySelector('.notification-badge');
            if (badge) badge.remove();
            const icon = notificationElement.querySelector('.notification-unread-icon');
            if (icon) {
                icon.className = 'fas fa-check-circle notification-read-icon';
            }
        }
        
        // 如果有关联的问题，跳转到问题详情
        if (bugId) {
            window.location.href = `/bug/${bugId}`;
        }
    } catch (error) {
        console.error('标记已读失败:', error);
    }
}

// 标记所有通知为已读
async function markAllAsRead() {
    try {
        const response = await fetch('/api/notifications/read-all', {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            // 更新所有通知的状态
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                const badge = item.querySelector('.notification-badge');
                if (badge) badge.remove();
                const icon = item.querySelector('.notification-unread-icon');
                if (icon) {
                    icon.className = 'fas fa-check-circle notification-read-icon';
                }
            });

            // 显示成功提示
            showToast('所有通知已标记为已读', 'success');
        } else {
            showToast('操作失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('标记全部已读失败:', error);
        showToast('操作失败', 'error');
    }
}

// 显示Toast提示
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    toast.style.cssText = `
        padding: 15px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;

    // 根据类型设置颜色
    switch (type) {
        case 'success':
            toast.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            break;
        case 'error':
            toast.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
            break;
        default:
            toast.style.background = 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)';
    }

    toast.textContent = message;
    toastContainer.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
