@echo off
REM ReBugTracker Windows服务管理脚本
REM 提供启动、停止、重启、状态查看等功能

setlocal enabledelayedexpansion

REM 服务配置
set "SERVICE_NAME=ReBugTracker"
set "PROJECT_DIR=%~dp0.."

:main_menu
cls
echo ReBugTracker Windows服务管理工具
echo ==========================================
echo.

REM 检查服务是否存在
sc query "%SERVICE_NAME%" >nul 2>&1
if errorlevel 1 (
    echo [错误] 服务 %SERVICE_NAME% 未安装
    echo.
    echo 请先运行安装脚本: deployment_tools\install_windows_service.bat
    pause
    exit /b 1
)

REM 获取服务状态
for /f "tokens=4" %%i in ('sc query "%SERVICE_NAME%" ^| findstr "STATE"') do set SERVICE_STATE=%%i

echo 当前服务状态:
if "%SERVICE_STATE%"=="RUNNING" (
    echo [运行中] 服务正在运行
    echo 访问地址: http://localhost:8000
) else if "%SERVICE_STATE%"=="STOPPED" (
    echo [已停止] 服务已停止
) else (
    echo [状态] %SERVICE_STATE%
)
echo.

echo 管理选项:
echo ==========================================
echo 1) 启动服务
echo 2) 停止服务  
echo 3) 重启服务
echo 4) 查看服务状态
echo 5) 查看日志文件
echo 6) 服务配置管理
echo 7) 卸载服务
echo 8) 打开Web界面
echo 0) 退出
echo.

set /p choice="请选择操作 (0-8): "

if "%choice%"=="1" goto start_service
if "%choice%"=="2" goto stop_service
if "%choice%"=="3" goto restart_service
if "%choice%"=="4" goto show_status
if "%choice%"=="5" goto view_logs
if "%choice%"=="6" goto config_service
if "%choice%"=="7" goto uninstall_service
if "%choice%"=="8" goto open_web
if "%choice%"=="0" goto exit_script

echo [错误] 无效选择，请输入 0-8
pause
goto main_menu

:start_service
echo.
echo [信息] 启动服务...
net start "%SERVICE_NAME%"
if errorlevel 1 (
    echo [错误] 服务启动失败
    echo 请检查日志文件获取详细信息
) else (
    echo [成功] 服务启动成功
    echo 访问地址: http://localhost:8000
)
pause
goto main_menu

:stop_service
echo.
echo [信息] 停止服务...
net stop "%SERVICE_NAME%"
if errorlevel 1 (
    echo [错误] 服务停止失败
) else (
    echo [成功] 服务已停止
)
pause
goto main_menu

:restart_service
echo.
echo [信息] 重启服务...
echo 正在停止服务...
net stop "%SERVICE_NAME%" >nul 2>&1
timeout /t 3 /nobreak >nul
echo 正在启动服务...
net start "%SERVICE_NAME%"
if errorlevel 1 (
    echo [错误] 服务重启失败
) else (
    echo [成功] 服务重启成功
    echo 访问地址: http://localhost:8000
)
pause
goto main_menu

:show_status
echo.
echo 详细服务状态:
echo ==========================================
sc query "%SERVICE_NAME%"
echo.
sc qc "%SERVICE_NAME%"
pause
goto main_menu

:view_logs
echo.
echo 日志文件查看:
echo ==========================================
echo 1) 查看标准输出日志
echo 2) 查看错误输出日志
echo 3) 打开日志目录
echo 4) 返回主菜单
echo.

set /p log_choice="请选择 (1-4): "

if "%log_choice%"=="1" (
    if exist "%PROJECT_DIR%\logs\service_stdout.log" (
        echo.
        echo 标准输出日志 (最后20行):
        echo ==========================================
        powershell "Get-Content '%PROJECT_DIR%\logs\service_stdout.log' -Tail 20"
    ) else (
        echo [警告] 标准输出日志文件不存在
    )
) else if "%log_choice%"=="2" (
    if exist "%PROJECT_DIR%\logs\service_stderr.log" (
        echo.
        echo 错误输出日志 (最后20行):
        echo ==========================================
        powershell "Get-Content '%PROJECT_DIR%\logs\service_stderr.log' -Tail 20"
    ) else (
        echo [警告] 错误输出日志文件不存在
    )
) else if "%log_choice%"=="3" (
    if exist "%PROJECT_DIR%\logs" (
        explorer "%PROJECT_DIR%\logs"
    ) else (
        echo [警告] 日志目录不存在
    )
) else if "%log_choice%"=="4" (
    goto main_menu
) else (
    echo [错误] 无效选择
)
pause
goto main_menu

:config_service
echo.
echo 服务配置管理:
echo ==========================================
echo 1) 查看当前配置
echo 2) 设置为自动启动
echo 3) 设置为手动启动
echo 4) 返回主菜单
echo.

set /p config_choice="请选择 (1-4): "

if "%config_choice%"=="1" (
    echo.
    sc qc "%SERVICE_NAME%"
) else if "%config_choice%"=="2" (
    echo.
    echo [信息] 设置服务为自动启动...
    sc config "%SERVICE_NAME%" start= auto
    echo [成功] 服务已设置为自动启动
) else if "%config_choice%"=="3" (
    echo.
    echo [信息] 设置服务为手动启动...
    sc config "%SERVICE_NAME%" start= demand
    echo [成功] 服务已设置为手动启动
) else if "%config_choice%"=="4" (
    goto main_menu
) else (
    echo [错误] 无效选择
)
pause
goto main_menu

:uninstall_service
echo.
echo [警告] 确定要卸载 ReBugTracker Windows服务吗?
set /p confirm="输入 'yes' 确认卸载: "
if /i "%confirm%"=="yes" (
    echo.
    echo [信息] 正在卸载服务...
    call "%PROJECT_DIR%\deployment_tools\uninstall_windows_service.bat"
    exit /b 0
) else (
    echo 卸载已取消
    pause
    goto main_menu
)

:open_web
echo.
echo [信息] 打开Web界面...
if "%SERVICE_STATE%"=="RUNNING" (
    start http://localhost:8000
    echo [成功] 已在浏览器中打开 http://localhost:8000
) else (
    echo [错误] 服务未运行，无法访问Web界面
    echo 请先启动服务
)
pause
goto main_menu

:exit_script
echo.
echo 感谢使用 ReBugTracker 服务管理工具！
exit /b 0