{% extends "base.html" %}

{% block content %}
<style>
    .dashboard-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: calc(100vh - 80px);
        padding: 20px 0;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .header-top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .header-left {
        flex: 1;
    }

    .header-title {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-subtitle {
        margin: 5px 0 0 0;
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 400;
    }

    .header-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 15px;
    }

    .user-info-row {
        display: flex;
        align-items: center;
        gap: 15px;
        text-align: center;
    }

    .user-name {
        font-size: 1.2rem;
        font-weight: 600;
    }

    .user-role {
        font-size: 1rem;
        opacity: 0.9;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .user-team {
        font-size: 1rem;
        opacity: 0.9;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .header-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-modern {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-modern:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        text-decoration: none;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-danger-modern {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-info-modern {
        background: linear-gradient(135deg, #38f9d7, #4dd0e1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white !important;
    }

    .btn-info-modern:hover {
        background: linear-gradient(135deg, #26e6cb, #38f9d7) !important;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(56, 249, 215, 0.4) !important;
    }

    /* 新的现代化按钮样式 */
    .btn-modern.btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white !important;
    }

    .btn-modern.btn-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-modern.btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white !important;
    }

    .btn-modern.btn-success:hover {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-modern.btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border: none;
        color: white !important;
    }

    .btn-modern.btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-modern.btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border: none;
        color: white !important;
    }

    .btn-modern.btn-info:hover {
        background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .btn-modern.btn-outline-primary {
        background: transparent;
        border: 2px solid #007bff;
        color: #007bff !important;
    }

    .btn-modern.btn-outline-primary:hover {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #007bff;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-modern.btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d !important;
    }

    .btn-modern.btn-outline-secondary:hover {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border-color: #6c757d;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-modern.btn-outline-info {
        background: transparent;
        border: 2px solid #17a2b8;
        color: #17a2b8 !important;
    }

    .btn-modern.btn-outline-info:hover {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border-color: #17a2b8;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    /* 字段选择快捷操作按钮样式 */
    .field-actions {
        justify-content: center;
        align-items: center;
    }

    /* 通知中心样式 */
    .notification-center {
        position: relative;
        display: inline-block;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 10px;
        min-width: 18px;
        text-align: center;
        z-index: 10;
    }

    .header-actions .dropdown {
        position: relative;
    }

    /* 确保通知下拉菜单不被其他元素遮挡 */
    .notification-dropdown.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 动态调整通知位置的类 */
    .notification-dropdown.position-bottom {
        top: 100% !important;
        transform: translateX(-10px) translateY(10px) !important;
    }

    .notification-dropdown.position-top {
        top: 0 !important;
        transform: translateX(-10px) translateY(-100%) !important;
    }

    .notification-dropdown {
        position: fixed !important;
        top: 60px !important;
        right: 20px !important;
        min-width: 380px;
        max-width: 450px;
        max-height: 80vh;
        border: none;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        z-index: 99999 !important;
        background: white;
        padding: 0;
        margin-top: 0;
        display: none;
        overflow: visible;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .notification-dropdown {
            position: fixed !important;
            left: 5vw !important;
            right: 5vw !important;
            top: auto !important;
            bottom: auto !important;
            min-width: auto !important;
            max-width: none !important;
            width: 90vw !important;
            transform: none !important;
        }
    }

    .notification-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        font-weight: 600;
        color: #333;
        background: #f8f9fa;
        border-radius: 15px 15px 0 0;
    }

    .notification-item {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .notification-item:hover {
        background-color: rgba(102, 126, 234, 0.05) !important;
    }

    .notification-item.unread {
        background-color: rgba(0, 123, 255, 0.05);
        border-left: 4px solid #007bff;
    }

    .notification-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 10px;
        margin-top: 5px;
    }

    .notification-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
        font-size: 14px;
        line-height: 1.4;
    }

    .notification-content {
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 8px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: pre-wrap;
    }

    .notification-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #999;
    }

    .notification-time {
        font-size: 11px;
        color: #999;
    }

    .notification-type {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;
    }

    /* 统计卡片样式 */
    .stats-container {
        width: 100%;
        margin: 30px 0;
    }

    .stats-grid-inline {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 12px;
        margin-bottom: 30px;
    }

    .stat-card-inline {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 12px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
        min-height: 80px;
        justify-content: center;
    }

    .stat-card-inline:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: rgba(102, 126, 234, 0.5);
        background: rgba(255, 255, 255, 1);
    }

    .stat-card-inline.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .stat-icon-inline {
        width: 32px;
        height: 32px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        flex-shrink: 0;
        margin-bottom: 4px;
    }

    .stat-content-inline {
        text-align: center;
        width: 100%;
    }

    .stat-number-inline {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        line-height: 1.2;
        transition: color 0.3s ease;
        margin-bottom: 2px;
    }

    .stat-label-inline {
        font-size: 0.7rem;
        color: #666;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: color 0.3s ease;
        white-space: nowrap;
    }

    .stat-card-inline.active .stat-number-inline,
    .stat-card-inline.active .stat-label-inline {
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* 管理区域样式 */
    .filter-section-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
        padding: 20px 0;
    }

    .filter-section-inline {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .filter-title {
        color: #333;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        white-space: nowrap;
    }

    .filter-options {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        flex: 1;
    }

    .filter-checkbox {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.1), rgba(56, 249, 215, 0.1));
        border: 1px solid rgba(67, 233, 123, 0.2);
        padding: 6px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 5px;
        color: #333;
        font-weight: 500;
    }

    .filter-checkbox:hover {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.2), rgba(56, 249, 215, 0.2));
        border-color: rgba(67, 233, 123, 0.4);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(67, 233, 123, 0.2);
    }

    .filter-checkbox input:checked + span {
        color: #2d5a3d;
        font-weight: 600;
    }

    .bugs-section {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .bugs-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
        color: white;
        padding: 25px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .bugs-header.users-theme {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
    }

    .bugs-header.bugs-theme {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 50%, #4facfe 100%);
    }

    .bugs-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
        animation: shimmer 4s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .bugs-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
    }

    .bugs-count {
        font-size: 1rem;
        opacity: 0.95;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
    }

    /* 表格样式 */
    .table-container {
        padding: 30px;
        background: white;
    }

    .table {
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    .table thead th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: none;
        padding: 15px;
        font-weight: 600;
        color: #495057;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid #dee2e6;
    }

    .table tbody tr {
        transition: all 0.3s ease;
        border: none;
    }

    .table tbody tr:nth-child(even) {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(240, 147, 251, 0.08));
    }

    .table tbody tr:nth-child(odd) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(67, 233, 123, 0.06));
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.18), rgba(240, 147, 251, 0.18), rgba(67, 233, 123, 0.12));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
    }

    /* 用户管理表格特殊样式 */
    #usersTableBody tr:nth-child(even) {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.12), rgba(245, 87, 108, 0.08), rgba(79, 172, 254, 0.06));
    }

    #usersTableBody tr:nth-child(odd) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(240, 147, 251, 0.04));
    }

    #usersTableBody tr:hover {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.25), rgba(245, 87, 108, 0.20), rgba(79, 172, 254, 0.15));
        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        transform: translateY(-3px);
    }

    /* 问题管理表格特殊样式 */
    #bugsTableBody tr:nth-child(even) {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.12), rgba(56, 249, 215, 0.08), rgba(79, 172, 254, 0.06));
    }

    #bugsTableBody tr:nth-child(odd) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(67, 233, 123, 0.04));
    }

    #bugsTableBody tr:hover {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.25), rgba(56, 249, 215, 0.20), rgba(79, 172, 254, 0.15));
        box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3);
        transform: translateY(-3px);
    }

    .table tbody td {
        padding: 15px;
        border: none;
        vertical-align: middle;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* 状态徽章样式 */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        display: inline-block;
    }

    .status-pending { background: linear-gradient(135deg, #ffeaa7, #fdcb6e); color: #d63031; }
    .status-assigned { background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; }
    .status-processing { background: linear-gradient(135deg, #fd79a8, #e84393); color: white; }
    .status-resolved { background: linear-gradient(135deg, #00b894, #00a085); color: white; }
    .status-completed { background: linear-gradient(135deg, #636e72, #2d3436); color: white; }

    /* 按钮样式 */
    .btn-action {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.8rem;
        border: none;
        transition: all 0.3s ease;
        margin: 2px;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        text-decoration: none;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        text-decoration: none;
    }

    .btn-outline-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: 1px solid #667eea;
    }

    .btn-outline-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: 1px solid #dc3545;
    }

    .btn-outline-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        border: 1px solid #17a2b8;
    }

    /* 选项卡样式 */
    .tab-container {
        margin-top: 30px;
    }

    .tab-nav {
        display: flex;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px 15px 0 0;
        overflow: hidden;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
    }

    .tab-button {
        flex: 1;
        background: transparent;
        border: none;
        padding: 15px 20px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: relative;
    }

    .tab-button:hover {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        transform: translateY(-1px);
    }

    .tab-button.active {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        box-shadow: inset 0 -3px 0 #fff;
        transform: translateY(-1px);
    }

    /* 用户管理选项卡激活状态 */
    .tab-button.active#usersTab {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.3), rgba(245, 87, 108, 0.3));
        box-shadow: inset 0 -3px 0 #f093fb;
    }

    /* 问题管理选项卡激活状态 */
    .tab-button.active#bugsTab {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.3), rgba(56, 249, 215, 0.3));
        box-shadow: inset 0 -3px 0 #43e97b;
    }

    /* 通知配置选项卡激活状态 */
    .tab-button.active#notificationsTab {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 152, 0, 0.3));
        box-shadow: inset 0 -3px 0 #ffc107;
    }

    /* 报表导出选项卡激活状态 */
    .tab-button.active#reportsTab {
        background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(233, 30, 99, 0.3));
        box-shadow: inset 0 -3px 0 #9c27b0;
    }

    .tab-content {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 0 0 20px 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-top: none;
        position: relative;
    }

    .tab-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(240, 147, 251, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(67, 233, 123, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
    }

    .tab-pane {
        position: relative;
        z-index: 1;
    }

    .tab-pane {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }

    .tab-pane.active {
        display: block;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .header-top-row {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .header-title {
            font-size: 2rem;
        }

        .stats-grid-inline {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }

        .stat-card-inline {
            padding: 10px;
            min-height: 70px;
        }

        .stat-number-inline {
            font-size: 1rem;
        }

        .stat-label-inline {
            font-size: 0.65rem;
        }

        .table-container {
            padding: 15px;
            overflow-x: auto;
        }

        .table {
            min-width: 600px;
        }

        .tab-button {
            font-size: 0.9rem;
            padding: 12px 15px;
        }
    }

    /* 模态框样式 */
    .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        border: none;
        padding: 20px 30px;
    }

    .modal-body {
        padding: 30px;
    }

    .modal-footer {
        border: none;
        padding: 20px 30px;
        background: #f8f9fa;
        border-radius: 0 0 20px 20px;
    }

    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a4190);
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    /* 分配状态样式 */
    .assignee-name {
        color: #28a745;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .unassigned {
        color: #dc3545;
        font-weight: 500;
        display: flex;
        align-items: center;
        font-style: italic;
    }

    .assignee-name i,
    .unassigned i {
        font-size: 0.9rem;
    }

    /* 通知列表滚动条样式 */
    #notificationList::-webkit-scrollbar {
        width: 6px;
    }

    #notificationList::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #notificationList::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    #notificationList::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 现代化Modal样式 */
    .modern-modal {
        border: none;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    /* 确认删除Modal样式 */
    .confirm-delete-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .confirm-delete-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .confirm-delete-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .warning-animation {
        margin-bottom: 25px;
    }

    .warning-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        animation: pulse 2s infinite;
    }

    .confirm-delete-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }

    .warning-text {
        color: #dc3545;
        font-weight: 500;
        margin: 0;
    }

    .confirm-delete-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        display: flex;
        gap: 15px;
        justify-content: center;
    }

    .confirm-delete-footer .btn-modern {
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .confirm-delete-footer .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        color: white !important;
    }

    .confirm-delete-footer .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .confirm-delete-footer .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white !important;
    }

    .confirm-delete-footer .btn-danger:hover {
        background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    /* 成功Modal样式 */
    .success-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .success-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .success-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .success-animation {
        margin-bottom: 25px;
    }

    .checkmark {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #28a745;
        stroke-miterlimit: 10;
        margin: 0 auto 20px;
        box-shadow: inset 0px 0px 0px #28a745;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 2;
        stroke-miterlimit: 10;
        stroke: #28a745;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    .success-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .success-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        text-align: center;
    }

    .success-footer .btn-modern {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .success-footer .btn-modern:hover {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    /* 错误Modal样式 */
    .error-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .error-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .error-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .error-animation {
        margin-bottom: 25px;
    }

    .error-icon-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        animation: shake 0.5s ease-in-out;
    }

    .error-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .error-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        text-align: center;
    }

    .error-footer .btn-modern {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .error-footer .btn-modern:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    /* 动画效果 */
    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {
        0%, 100% {
            transform: none;
        }
        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 30px #28a745;
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes shake {
        0%, 100% {
            transform: translateX(0);
        }
        10%, 30%, 50%, 70%, 90% {
            transform: translateX(-5px);
        }
        20%, 40%, 60%, 80% {
            transform: translateX(5px);
        }
    }

    /* 通知配置页面样式 */
    .notifications-theme {
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        color: white;
        padding: 25px 30px;
        border-radius: 20px 20px 0 0;
        margin-bottom: 0;
    }

    .notifications-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .notifications-subtitle {
        margin: 8px 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    .notifications-content {
        background: white;
        border-radius: 0 0 20px 20px;
        padding: 30px;
    }

    .config-section {
        background: rgba(255, 193, 7, 0.05);
        border-radius: 15px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 193, 7, 0.2);
        overflow: hidden;
    }

    .config-header {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 193, 7, 0.2);
    }

    .config-header h3 {
        margin: 0;
        color: #333;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .config-body {
        padding: 20px;
    }

    .notification-types {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .notification-type-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: white;
        border-radius: 10px;
        border: 1px solid rgba(255, 193, 7, 0.2);
        transition: all 0.3s ease;
    }

    .notification-type-item:hover {
        background: rgba(255, 193, 7, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.15);
    }

    .type-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .type-info i {
        font-size: 1.5rem;
        width: 40px;
        text-align: center;
    }

    .type-info h5 {
        margin: 0;
        font-weight: 600;
        color: #333;
    }

    .type-info p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .config-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid rgba(255, 193, 7, 0.2);
    }

    .config-actions .btn {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .config-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    /* 清理管理样式 */
    .cleanup-stats-card {
        transition: all 0.3s ease;
    }

    .cleanup-stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
    }

    .stat-item {
        text-align: center;
        padding: 10px;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: #1976d2;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .cleanup-action-card {
        transition: all 0.3s ease;
        height: 100%;
    }

    .cleanup-action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* 用户通知偏好表格样式 */
    .user-info {
        min-width: 120px;
    }

    .user-info strong {
        color: #333;
        font-weight: 600;
    }

    .user-info small {
        font-size: 0.8rem;
        color: #666;
    }

    .table th {
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid rgba(255, 193, 7, 0.3);
        vertical-align: middle;
    }

    .table td {
        vertical-align: middle;
        border-bottom: 1px solid rgba(255, 193, 7, 0.1);
    }

    .form-check-input:checked {
        background-color: #ffc107;
        border-color: #ffc107;
    }

    .form-check-input:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    }

    .batch-operations {
        background: rgba(255, 193, 7, 0.05);
        padding: 15px;
        border-radius: 10px;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }

    .batch-operations h6 {
        color: #333;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .batch-operations .btn {
        border-radius: 20px;
        font-size: 0.8rem;
        padding: 6px 12px;
        font-weight: 500;
    }

    .batch-group {
        margin-bottom: 15px;
    }

    .batch-label {
        display: block;
        font-size: 0.85rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        text-align: center;
    }

    .batch-label i {
        margin-right: 5px;
        color: #ffc107;
    }

    .badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    /* 报表导出页面样式 */
    .reports-theme {
        background: linear-gradient(135deg, #9c27b0 0%, #e91e63 100%);
        color: white;
        padding: 25px 30px;
        border-radius: 20px 20px 0 0;
        margin-bottom: 0;
    }

    .reports-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .reports-subtitle {
        margin: 8px 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    .reports-content {
        background: white;
        border-radius: 0 0 20px 20px;
        padding: 30px;
    }

    .export-section {
        background: rgba(156, 39, 176, 0.05);
        border-radius: 15px;
        margin-bottom: 25px;
        border: 1px solid rgba(156, 39, 176, 0.2);
        overflow: hidden;
    }

    /* 图表相关样式 */
    .chart-controls {
        border-bottom: 1px solid rgba(156, 39, 176, 0.1);
        padding-bottom: 20px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        background: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .chart-container canvas {
        max-height: 270px !important;
    }

    .btn-group .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .btn-group .btn.active {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
        color: white;
    }

    .btn-outline-success.active {
        background-color: var(--bs-success);
        border-color: var(--bs-success);
        color: white;
    }

    /* 时间范围按钮自定义样式 */
    .quick-dates .btn {
        background-color: #fce4ec; /* 淡粉色背景 */
        border-color: #f8bbd9;
        color: #880e4f; /* 深粉色文字 */
        transition: all 0.3s ease;
    }

    .quick-dates .btn:hover {
        background-color: #f8bbd9; /* 悬停时稍深一点的粉色 */
        border-color: #f48fb1;
        color: #880e4f;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .quick-dates .btn:active,
    .quick-dates .btn.active {
        background-color: #ad1457 !important; /* 点击时的深色 */
        border-color: #ad1457 !important;
        color: white !important;
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    .quick-dates .btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(173, 20, 87, 0.25);
    }

    /* 导出背景选择弹窗样式 */
    #exportBackgroundModal .btn {
        min-height: 120px;
        transition: all 0.3s ease;
    }

    #exportBackgroundModal .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    #exportBackgroundModal .btn i {
        font-size: 2rem;
        display: block;
        margin-bottom: 8px;
    }

    #exportBackgroundModal .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    #exportBackgroundModal .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        border-bottom: none;
    }

    #exportBackgroundModal .modal-header .btn-close {
        filter: invert(1);
    }

    .export-header {
        background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(233, 30, 99, 0.1));
        padding: 15px 20px;
        border-bottom: 1px solid rgba(156, 39, 176, 0.2);
    }

    .export-header h3 {
        margin: 0;
        color: #333;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .export-body {
        padding: 20px;
    }

    .filter-options, .field-selection {
        background: white;
        border-radius: 10px;
        padding: 15px;
        border: 1px solid rgba(156, 39, 176, 0.1);
    }

    .status-filters {
        margin-top: 10px;
        padding: 10px;
        background: rgba(156, 39, 176, 0.05);
        border-radius: 8px;
    }

    .field-group {
        background: rgba(156, 39, 176, 0.03);
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .field-group h6 {
        color: #9c27b0;
        font-weight: 600;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(156, 39, 176, 0.2);
        padding-bottom: 5px;
    }

    .field-actions {
        text-align: center;
        padding-top: 15px;
        border-top: 1px solid rgba(156, 39, 176, 0.2);
    }

    .field-actions .btn {
        margin: 0 5px;
        border-radius: 20px;
    }

    .quick-dates .btn {
        margin: 2px;
        border-radius: 15px;
        font-size: 0.8rem;
    }

    .format-options {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }

    .format-options .form-check-label {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border: 1px solid rgba(156, 39, 176, 0.2);
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .format-options .form-check-input:checked + .form-check-label {
        background: rgba(156, 39, 176, 0.1);
        border-color: #9c27b0;
    }

    .export-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .export-actions .btn {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .export-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .preview-info {
        margin-bottom: 15px;
    }

    .preview-info .badge {
        margin-right: 10px;
        font-size: 0.9rem;
        padding: 6px 12px;
    }

    #previewTable {
        font-size: 0.9rem;
    }

    #previewTable th {
        background: linear-gradient(135deg, rgba(156, 39, 176, 0.1), rgba(233, 30, 99, 0.1));
        color: #333;
        font-weight: 600;
        border-bottom: 2px solid rgba(156, 39, 176, 0.3);
    }

    #previewTable td {
        border-bottom: 1px solid rgba(156, 39, 176, 0.1);
    }
</style>

<div class="dashboard-container">
    <div class="container">
        <!-- 头部区域 -->
        <div class="dashboard-header">
            <div class="header-content">
                <!-- 第一行：标题和用户信息 -->
                <div class="header-top-row">
                    <div class="header-left">
                        <h1 class="header-title">
                            <i class="fas fa-user-shield"></i>
                            管理员控制台
                        </h1>
                        <p class="header-subtitle">系统管理和数据监控中心</p>
                    </div>

                    <div class="header-right">
                        <div class="user-info-row">
                            <span class="user-name">{{ user.chinese_name or user.username }}</span>
                            <span class="user-role">
                                <i class="fas fa-user-shield"></i> 管理员
                            </span>
                            {% if user.team %}
                            <span class="user-team">
                                <i class="fas fa-building"></i> {{ user.team }}
                            </span>
                            {% endif %}
                        </div>

                        <div class="header-actions">
                            <!-- 通知按钮 -->
                            <div class="dropdown">
                                <button class="btn-modern btn-info-modern dropdown-toggle" type="button" id="notificationDropdown" aria-expanded="false" onclick="toggleNotificationDropdown(event); return false;">
                                    <i class="fas fa-bell"></i>
                                    通知
                                    <span class="notification-badge" id="notificationBadge">1</span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-start notification-dropdown" aria-labelledby="notificationDropdown">
                                    <li class="dropdown-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 20px; margin: 0; border-radius: 15px 15px 0 0;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="fas fa-bell me-2"></i>
                                                <strong>通知中心</strong>
                                            </div>
                                            <button class="btn btn-sm btn-light" onclick="markAllAsRead(event)" style="border-radius: 20px; font-size: 11px;">
                                                <i class="fas fa-check-double me-1"></i>全部已读
                                            </button>
                                        </div>
                                    </li>
                                    <li style="padding: 0; margin: 0; background: white;">
                                        <div id="notificationList" style="max-height: 400px; overflow-y: auto; background: white;">
                                            <!-- 通知内容将通过JavaScript动态加载 -->
                                            <div class="text-center p-4">
                                                <i class="fas fa-spinner fa-spin text-primary"></i>
                                                <div class="mt-2 text-muted">加载中...</div>
                                            </div>
                                        </div>
                                    </li>
                                    <li style="border-top: 1px solid rgba(0,0,0,0.08); margin: 0; background: white;">
                                        <a class="dropdown-item text-center py-3" href="/notifications" style="color: #667eea; font-weight: 500;">
                                            <i class="fas fa-external-link-alt me-2"></i>查看全部通知
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <a href="/logout" class="btn-modern btn-danger-modern">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if request.args.get('message') %}
        <div class="alert alert-success" style="padding:10px; background:#dff0d8; color:#3c763d; border-radius:4px; margin-bottom:15px;">
            {{ request.args.get('message') }}
        </div>
        {% endif %}

        <!-- 统计卡片区域 -->
        <div class="stats-container">
            <div class="stats-grid-inline">
                <div class="stat-card-inline clickable" data-status="all" onclick="filterByStatus('all')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="totalUsers">{{ total_users }}</div>
                        <div class="stat-label-inline">总用户数</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="bugs" onclick="filterByStatus('bugs')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="totalBugs">{{ bugs|length }}</div>
                        <div class="stat-label-inline">总问题数</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="pending" onclick="filterByStatus('pending')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #ffeaa7, #fdcb6e);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="pendingBugs">0</div>
                        <div class="stat-label-inline">待处理</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="assigned" onclick="filterByStatus('assigned')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #74b9ff, #0984e3);">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="assignedBugs">0</div>
                        <div class="stat-label-inline">已分配</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="processing" onclick="filterByStatus('processing')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="processingBugs">0</div>
                        <div class="stat-label-inline">处理中</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="resolved" onclick="filterByStatus('resolved')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #00b894, #00a085);">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="resolvedBugs">0</div>
                        <div class="stat-label-inline">已解决</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="completed" onclick="filterByStatus('completed')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #6c757d, #495057);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="completedBugs">0</div>
                        <div class="stat-label-inline">已完成</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 选项卡管理区域 -->
        <div class="tab-container">
            <!-- 选项卡导航 -->
            <div class="tab-nav">
                <button class="tab-button active" onclick="switchTab('users')" id="usersTab">
                    <i class="fas fa-users"></i>
                    用户管理
                    <span class="badge" style="background: rgba(255,255,255,0.25); padding: 3px 8px; border-radius: 12px; font-size: 0.75rem; margin-left: 8px; font-weight: 700; border: 1px solid rgba(255,255,255,0.3);">{{ users|length }}</span>
                </button>
                <button class="tab-button" onclick="switchTab('bugs')" id="bugsTab">
                    <i class="fas fa-bug"></i>
                    问题管理
                    <span class="badge" style="background: rgba(255,255,255,0.25); padding: 3px 8px; border-radius: 12px; font-size: 0.75rem; margin-left: 8px; font-weight: 700; border: 1px solid rgba(255,255,255,0.3);">{{ bugs|length }}</span>
                </button>
                <button class="tab-button" onclick="switchTab('notifications')" id="notificationsTab">
                    <i class="fas fa-bell"></i>
                    通知配置
                    <span class="badge" style="background: rgba(255,255,255,0.25); padding: 3px 8px; border-radius: 12px; font-size: 0.75rem; margin-left: 8px; font-weight: 700; border: 1px solid rgba(255,255,255,0.3);">系统</span>
                </button>
                <button class="tab-button" onclick="switchTab('reports')" id="reportsTab">
                    <i class="fas fa-file-export"></i>
                    报表导出
                    <span class="badge" style="background: rgba(255,255,255,0.25); padding: 3px 8px; border-radius: 12px; font-size: 0.75rem; margin-left: 8px; font-weight: 700; border: 1px solid rgba(255,255,255,0.3);">Excel</span>
                </button>
            </div>

            <!-- 选项卡内容 -->
            <div class="tab-content">
                <!-- 用户管理选项卡 -->
                <div class="tab-pane active" id="usersPane">
                    <div class="bugs-header users-theme">
                        <h2 class="bugs-title">
                            <i class="fas fa-users"></i>
                            用户管理
                        </h2>
                        <div class="bugs-count">
                            <span id="visibleUsersCount">{{ users|length }}</span> 个用户
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>中文名</th>
                                    <th>邮箱</th>
                                    <th>电话</th>
                                    <th>角色</th>
                                    <th>团队</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                {% for user_item in users %}
                                <tr>
                                    <td>{{ user_item.id }}</td>
                                    <td>{{ user_item.username }}</td>
                                    <td>{{ user_item.chinese_name or '未设置' }}</td>
                                    <td>
                                        {% if user_item.email %}
                                            <a href="mailto:{{ user_item.email }}" style="color: #667eea; text-decoration: none;">
                                                <i class="fas fa-envelope" style="margin-right: 5px;"></i>
                                                {{ user_item.email }}
                                            </a>
                                        {% else %}
                                            <span style="color: #999;">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_item.phone %}
                                            <a href="tel:{{ user_item.phone }}" style="color: #28a745; text-decoration: none;">
                                                <i class="fas fa-phone" style="margin-right: 5px;"></i>
                                                {{ user_item.phone }}
                                            </a>
                                        {% else %}
                                            <span style="color: #999;">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_item.role_en == 'gly' %}
                                            <span class="status-badge" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">管理员</span>
                                        {% elif user_item.role_en == 'fzr' %}
                                            <span class="status-badge" style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white;">负责人</span>
                                        {% elif user_item.role_en == 'ssz' %}
                                            <span class="status-badge" style="background: linear-gradient(135deg, #00b894, #00a085); color: white;">实施组</span>
                                        {% elif user_item.role_en == 'zncy' %}
                                            <span class="status-badge" style="background: linear-gradient(135deg, #fd79a8, #e84393); color: white;">组内成员</span>
                                        {% else %}
                                            <span class="status-badge" style="background: linear-gradient(135deg, #636e72, #2d3436); color: white;">{{ user_item.role }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user_item.team or '未分配' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="editUser({{ user_item.id }})">
                                            <i class="fas fa-edit"></i>
                                            编辑
                                        </button>
                                        {% if user_item.id != user.id %}
                                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser({{ user_item.id }})">
                                            <i class="fas fa-trash"></i>
                                            删除
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 问题管理选项卡 -->
                <div class="tab-pane" id="bugsPane">
                    <div class="bugs-header bugs-theme">
                        <h2 class="bugs-title">
                            <i class="fas fa-bug"></i>
                            问题管理
                        </h2>
                        <div class="bugs-count">
                            <span id="visibleBugsCount">{{ bugs|length }}</span> 个问题
                        </div>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="filter-section-inline" style="padding: 20px 30px; border-bottom: 1px solid #eee;">
                        <div class="filter-title">
                            <i class="fas fa-filter"></i>
                            状态筛选
                        </div>
                        <div class="filter-options">
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="all" checked>
                                <span>全部</span>
                            </label>
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="待处理">
                                <span>待处理</span>
                            </label>
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="已分配">
                                <span>已分配</span>
                            </label>
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="处理中">
                                <span>处理中</span>
                            </label>
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="已解决">
                                <span>已解决</span>
                            </label>
                            <label class="filter-checkbox">
                                <input type="checkbox" class="status-checkbox" value="已完成">
                                <span>已完成</span>
                            </label>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>状态</th>
                                    <th>创建者</th>
                                    <th>分配给</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="bugsTableBody">
                                {% for bug in bugs %}
                                <tr data-status="{{ bug.status }}">
                                    <td>{{ bug.id }}</td>
                                    <td>
                                        <a href="/bug/{{ bug.id }}" style="text-decoration: none; color: #667eea; font-weight: 500;">
                                            {{ bug.title }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ bug.status.replace(' ', '-') }}">
                                            {{ bug.status }}
                                        </span>
                                    </td>
                                    <td>{{ bug.creator_name or '未知' }}</td>
                                    <td>
                                        {% if bug.assignee_name %}
                                            <span class="assignee-name">
                                                <i class="fas fa-user-check" style="color: #28a745; margin-right: 5px;"></i>
                                                {{ bug.assignee_name }}
                                            </span>
                                        {% else %}
                                            <span class="unassigned">
                                                <i class="fas fa-user-times" style="color: #dc3545; margin-right: 5px;"></i>
                                                未分配
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ bug.created_at }}</td>
                                    <td>
                                        <a href="/bug/{{ bug.id }}" class="btn btn-sm btn-outline-info btn-action">
                                            <i class="fas fa-eye"></i>
                                            查看
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteBugAdmin({{ bug.id }})">
                                            <i class="fas fa-trash"></i>
                                            删除
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 通知配置选项卡 -->
                <div class="tab-pane" id="notificationsPane">
                    <div class="notifications-header notifications-theme">
                        <h2 class="notifications-title">
                            <i class="fas fa-bell"></i>
                            通知配置
                        </h2>
                        <p class="notifications-subtitle">管理系统通知设置和配置</p>
                    </div>

                    <div class="notifications-content">
                        <!-- 全局通知设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-globe"></i> 全局通知设置</h3>
                            </div>
                            <div class="config-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-toggle-on"></i>
                                                启用系统通知
                                            </label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableServerNotifications" checked>
                                                <label class="form-check-label" for="enableServerNotifications">
                                                    开启服务器级通知功能
                                                </label>
                                            </div>
                                            <small class="text-muted">关闭后将停止所有通知发送</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-clock"></i>
                                                应用内通知保留时间
                                            </label>
                                            <select class="form-select" id="notificationRetention">
                                                <option value="7">7天</option>
                                                <option value="30" selected>30天</option>
                                                <option value="90">90天</option>
                                                <option value="365">1年</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-magic"></i>
                                                自动清理功能
                                            </label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableAutoCleanup">
                                                <label class="form-check-label" for="enableAutoCleanup">
                                                    启用自动清理过期和超量通知
                                                </label>
                                            </div>
                                            <small class="text-muted">开启后系统将每24小时自动清理过期和超量的通知</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 应用内通知设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-bell"></i> 应用内通知设置</h3>
                            </div>
                            <div class="config-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableInAppNotifications" checked>
                                                <label class="form-check-label" for="enableInAppNotifications">
                                                    <strong>启用应用内通知</strong>
                                                </label>
                                            </div>
                                            <small class="text-muted">在系统内显示通知消息</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">每用户最大通知数</label>
                                            <input type="number" class="form-control" id="maxNotificationsPerUser" value="100" min="10" max="1000">
                                            <small class="text-muted">超出后自动清理旧通知</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 邮件通知设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-envelope"></i> 邮件通知设置</h3>
                            </div>
                            <div class="config-body">
                                <div class="form-group mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableEmailNotifications">
                                        <label class="form-check-label" for="enableEmailNotifications">
                                            <strong>启用邮件通知</strong>
                                        </label>
                                    </div>
                                    <small class="text-muted">通过邮件发送通知消息</small>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">SMTP服务器</label>
                                            <input type="text" class="form-control" id="smtpServer" placeholder="smtp.gmail.com">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">SMTP端口</label>
                                            <input type="number" class="form-control" id="smtpPort" value="587">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">发送邮箱用户名</label>
                                            <input type="text" class="form-control" id="smtpUsername" placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">邮箱密码/授权码</label>
                                            <input type="password" class="form-control" id="smtpPassword" placeholder="邮箱授权码">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">发件人邮箱</label>
                                            <input type="email" class="form-control" id="fromEmail" placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">发件人名称</label>
                                            <input type="text" class="form-control" id="fromName" value="ReBugTracker">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="smtpUseTls" checked>
                                        <label class="form-check-label" for="smtpUseTls">
                                            使用TLS加密
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gotify通知设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-mobile-alt"></i> Gotify推送通知设置</h3>
                            </div>
                            <div class="config-body">
                                <div class="form-group mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableGotifyNotifications">
                                        <label class="form-check-label" for="enableGotifyNotifications">
                                            <strong>启用Gotify推送通知</strong>
                                        </label>
                                    </div>
                                    <small class="text-muted">通过Gotify服务器发送推送通知到移动设备</small>
                                </div>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="form-label">Gotify服务器地址</label>
                                            <input type="url" class="form-control" id="gotifyServerUrl" placeholder="http://localhost:8080">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">默认优先级</label>
                                            <select class="form-select" id="gotifyDefaultPriority">
                                                <option value="1">1 - 最低</option>
                                                <option value="3">3 - 低</option>
                                                <option value="5" selected>5 - 普通</option>
                                                <option value="7">7 - 高</option>
                                                <option value="10">10 - 最高</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">应用Token</label>
                                    <input type="password" class="form-control" id="gotifyAppToken" placeholder="Gotify应用Token">
                                    <small class="text-muted">在Gotify服务器中创建应用后获得的Token</small>
                                </div>
                            </div>
                        </div>

                        <!-- 通知流转规则设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-sitemap"></i> 通知流转规则</h3>
                            </div>
                            <div class="config-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>流转规则说明：</strong>根据ReBugTracker的工作流程，只向流程参与者发送相关通知
                                </div>
                                <div class="notification-types">
                                    <div class="notification-type-item">
                                        <div class="type-info">
                                            <i class="fas fa-plus-circle text-primary"></i>
                                            <div>
                                                <h5>问题创建通知</h5>
                                                <p><strong>触发：</strong>实施组提交新问题<br>
                                                <strong>通知对象：</strong>指定的负责人</p>
                                            </div>
                                        </div>
                                        <div class="type-controls">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="notifyBugCreated" checked>
                                                <label class="form-check-label" for="notifyBugCreated">启用</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="notification-type-item">
                                        <div class="type-info">
                                            <i class="fas fa-user-check text-success"></i>
                                            <div>
                                                <h5>问题分配通知</h5>
                                                <p><strong>触发：</strong>负责人分配问题给组内成员<br>
                                                <strong>通知对象：</strong>被分配的组内成员</p>
                                            </div>
                                        </div>
                                        <div class="type-controls">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="notifyBugAssigned" checked>
                                                <label class="form-check-label" for="notifyBugAssigned">启用</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="notification-type-item">
                                        <div class="type-info">
                                            <i class="fas fa-check-circle text-info"></i>
                                            <div>
                                                <h5>状态变更通知</h5>
                                                <p><strong>触发：</strong>问题状态发生变化<br>
                                                <strong>通知对象：</strong>创建者、分配者、当前处理人</p>
                                            </div>
                                        </div>
                                        <div class="type-controls">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="notifyBugStatusChanged" checked>
                                                <label class="form-check-label" for="notifyBugStatusChanged">启用</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="notification-type-item">
                                        <div class="type-info">
                                            <i class="fas fa-check-double text-warning"></i>
                                            <div>
                                                <h5>问题解决通知</h5>
                                                <p><strong>触发：</strong>组内成员解决问题<br>
                                                <strong>通知对象：</strong>实施组（创建者）和负责人</p>
                                            </div>
                                        </div>
                                        <div class="type-controls">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="notifyBugResolved" checked>
                                                <label class="form-check-label" for="notifyBugResolved">启用</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="notification-type-item">
                                        <div class="type-info">
                                            <i class="fas fa-times-circle text-secondary"></i>
                                            <div>
                                                <h5>问题关闭通知</h5>
                                                <p><strong>触发：</strong>问题被关闭<br>
                                                <strong>通知对象：</strong>负责人和管理员</p>
                                            </div>
                                        </div>
                                        <div class="type-controls">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="notifyBugClosed" checked>
                                                <label class="form-check-label" for="notifyBugClosed">启用</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户通知偏好设置 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-user-cog"></i> 用户通知偏好设置</h3>
                            </div>
                            <div class="config-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>说明：</strong>为每个用户单独配置三种通知渠道的开关状态
                                </div>

                                <!-- 用户通知偏好表格 -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));">
                                                <th>用户</th>
                                                <th>角色</th>
                                                <th>团队</th>
                                                <th class="text-center">
                                                    <i class="fas fa-bell"></i>
                                                    应用内通知
                                                </th>
                                                <th class="text-center">
                                                    <i class="fas fa-envelope"></i>
                                                    邮件通知
                                                </th>
                                                <th class="text-center">
                                                    <i class="fas fa-mobile-alt"></i>
                                                    Gotify通知
                                                </th>
                                                <th class="text-center">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="userNotificationPreferences">
                                            {% for user_item in users %}
                                            <tr data-user-id="{{ user_item.id }}">
                                                <td>
                                                    <div class="user-info">
                                                        <strong>{{ user_item.chinese_name or user_item.username }}</strong>
                                                        {% if user_item.chinese_name %}
                                                            <small class="text-muted d-block">{{ user_item.username }}</small>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    {% if user_item.role_en == 'gly' %}
                                                        <span class="badge bg-primary">管理员</span>
                                                    {% elif user_item.role_en == 'fzr' %}
                                                        <span class="badge bg-info">负责人</span>
                                                    {% elif user_item.role_en == 'ssz' %}
                                                        <span class="badge bg-success">实施组</span>
                                                    {% elif user_item.role_en == 'zncy' %}
                                                        <span class="badge bg-warning">组内成员</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ user_item.role }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ user_item.team or '未分配' }}</td>
                                                <td class="text-center">
                                                    <div class="form-check form-switch d-flex justify-content-center">
                                                        <input class="form-check-input" type="checkbox"
                                                               id="inapp_{{ user_item.id }}"
                                                               data-user-id="{{ user_item.id }}"
                                                               data-channel="inapp"
                                                               checked>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="form-check form-switch d-flex justify-content-center">
                                                        <input class="form-check-input" type="checkbox"
                                                               id="email_{{ user_item.id }}"
                                                               data-user-id="{{ user_item.id }}"
                                                               data-channel="email"
                                                               checked>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="form-check form-switch d-flex justify-content-center">
                                                        <input class="form-check-input" type="checkbox"
                                                               id="gotify_{{ user_item.id }}"
                                                               data-user-id="{{ user_item.id }}"
                                                               data-channel="gotify"
                                                               checked>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <button class="btn btn-sm btn-outline-primary"
                                                            onclick="saveUserNotificationPreference({{ user_item.id }})">
                                                        <i class="fas fa-save"></i>
                                                        保存
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 批量操作 -->
                                <div class="batch-operations mt-3">
                                    <h6><i class="fas fa-users"></i> 批量操作</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="batch-group">
                                                <label class="batch-label">
                                                    <i class="fas fa-bell"></i> 应用内通知
                                                </label>
                                                <div class="btn-group w-100" role="group">
                                                    <button class="btn btn-outline-success btn-sm" onclick="batchToggleNotification('inapp', true)">
                                                        <i class="fas fa-check"></i> 全部启用
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" onclick="batchToggleNotification('inapp', false)">
                                                        <i class="fas fa-times"></i> 全部禁用
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="batch-group">
                                                <label class="batch-label">
                                                    <i class="fas fa-envelope"></i> 邮件通知
                                                </label>
                                                <div class="btn-group w-100" role="group">
                                                    <button class="btn btn-outline-success btn-sm" onclick="batchToggleNotification('email', true)">
                                                        <i class="fas fa-check"></i> 全部启用
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" onclick="batchToggleNotification('email', false)">
                                                        <i class="fas fa-times"></i> 全部禁用
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="batch-group">
                                                <label class="batch-label">
                                                    <i class="fas fa-mobile-alt"></i> Gotify通知
                                                </label>
                                                <div class="btn-group w-100" role="group">
                                                    <button class="btn btn-outline-success btn-sm" onclick="batchToggleNotification('gotify', true)">
                                                        <i class="fas fa-check"></i> 全部启用
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" onclick="batchToggleNotification('gotify', false)">
                                                        <i class="fas fa-times"></i> 全部禁用
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 通知清理管理 -->
                        <div class="config-section">
                            <div class="config-header">
                                <h3><i class="fas fa-broom"></i> 通知清理管理</h3>
                                <p class="config-description">管理通知数据的清理和维护</p>
                            </div>
                            <div class="config-body">
                                <!-- 清理统计信息 -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="cleanup-stats-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 15px; padding: 20px; border: 1px solid #90caf9;">
                                            <h5 style="color: #1976d2; margin-bottom: 15px;">
                                                <i class="fas fa-chart-bar"></i> 清理统计信息
                                            </h5>
                                            <div class="row" id="cleanupStatsContainer">
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="totalNotifications">-</div>
                                                        <div class="stat-label">总通知数</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="userCount">-</div>
                                                        <div class="stat-label">用户数</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="retentionDays">-</div>
                                                        <div class="stat-label">保留天数</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="maxPerUser">-</div>
                                                        <div class="stat-label">每用户上限</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="expiredCount" style="color: #f57c00;">-</div>
                                                        <div class="stat-label">过期记录数</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="stat-item">
                                                        <div class="stat-value" id="excessCount" style="color: #e91e63;">-</div>
                                                        <div class="stat-label">过量记录数</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <small class="text-muted" id="oldestNotification">最旧通知: -</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 清理操作 -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="cleanup-action-card" style="background: #fff3e0; border: 1px solid #ffb74d; border-radius: 12px; padding: 20px; text-align: center;">
                                            <h6 style="color: #f57c00; margin-bottom: 10px;">
                                                <i class="fas fa-calendar-times"></i> 清理过期通知
                                            </h6>
                                            <p style="font-size: 0.9rem; color: #666; margin-bottom: 15px;">删除超过保留天数的通知</p>
                                            <button class="btn btn-warning btn-sm" onclick="cleanupNotifications('expired')" style="border-radius: 20px;">
                                                <i class="fas fa-trash-alt"></i> 清理过期
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="cleanup-action-card" style="background: #f3e5f5; border: 1px solid #ba68c8; border-radius: 12px; padding: 20px; text-align: center;">
                                            <h6 style="color: #8e24aa; margin-bottom: 10px;">
                                                <i class="fas fa-layer-group"></i> 清理超量通知
                                            </h6>
                                            <p style="font-size: 0.9rem; color: #666; margin-bottom: 15px;">删除超出数量限制的通知</p>
                                            <button class="btn btn-secondary btn-sm" onclick="cleanupNotifications('excess')" style="border-radius: 20px; background: #8e24aa; border-color: #8e24aa;">
                                                <i class="fas fa-compress-alt"></i> 清理超量
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="cleanup-action-card" style="background: #ffebee; border: 1px solid #f48fb1; border-radius: 12px; padding: 20px; text-align: center;">
                                            <h6 style="color: #c2185b; margin-bottom: 10px;">
                                                <i class="fas fa-broom"></i> 全面清理
                                            </h6>
                                            <p style="font-size: 0.9rem; color: #666; margin-bottom: 15px;">执行所有清理操作</p>
                                            <button class="btn btn-danger btn-sm" onclick="cleanupNotifications('all')" style="border-radius: 20px;">
                                                <i class="fas fa-magic"></i> 全面清理
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="config-actions text-center">
                            <button class="btn btn-primary btn-lg" onclick="saveAllNotificationConfig()" style="padding: 15px 40px; font-size: 1.1rem; border-radius: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
                                <i class="fas fa-save me-2"></i>
                                保存全局配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 报表导出选项卡 -->
                <div class="tab-pane" id="reportsPane">
                    <div class="reports-header reports-theme">
                        <h2 class="reports-title">
                            <i class="fas fa-file-export"></i>
                            报表导出
                        </h2>
                        <p class="reports-subtitle">导出可配置的问题列表和统计报表</p>
                    </div>

                    <div class="reports-content">
                        <!-- 导出配置 -->
                        <div class="export-section">
                            <div class="export-header">
                                <h3><i class="fas fa-cog"></i> 导出配置</h3>
                            </div>
                            <div class="export-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-filter"></i>
                                                筛选条件
                                            </label>
                                            <div class="filter-options">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="filterByStatus" checked>
                                                    <label class="form-check-label" for="filterByStatus">
                                                        按状态筛选
                                                    </label>
                                                </div>
                                                <div class="status-filters ms-3" id="statusFilters">
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="statusPending" checked>
                                                        <label class="form-check-label" for="statusPending">待处理</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="statusAssigned" checked>
                                                        <label class="form-check-label" for="statusAssigned">已分配</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="statusInProgress" checked>
                                                        <label class="form-check-label" for="statusInProgress">处理中</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="statusResolved" checked>
                                                        <label class="form-check-label" for="statusResolved">已解决</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="statusCompleted" checked>
                                                        <label class="form-check-label" for="statusCompleted">已完成</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-calendar-alt"></i>
                                                时间范围
                                            </label>
                                            <div class="date-range">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <input type="date" class="form-control" id="startDate" placeholder="开始日期">
                                                    </div>
                                                    <div class="col-6">
                                                        <input type="date" class="form-control" id="endDate" placeholder="结束日期">
                                                    </div>
                                                </div>
                                                <div class="quick-dates mt-2 d-flex flex-wrap gap-2">
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="setDateRange('week')">
                                                        <i class="fas fa-calendar-week me-1"></i>本周
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="setDateRange('month')">
                                                        <i class="fas fa-calendar-alt me-1"></i>本月
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="setDateRange('year')">
                                                        <i class="fas fa-calendar me-1"></i>本年
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="setDateRange('all')">
                                                        <i class="fas fa-infinity me-1"></i>全部
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 字段选择 -->
                        <div class="export-section">
                            <div class="export-header">
                                <h3><i class="fas fa-columns"></i> 导出字段</h3>
                            </div>
                            <div class="export-body">
                                <div class="field-selection">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h6>基本信息</h6>
                                            <div class="field-group">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldId" checked>
                                                    <label class="form-check-label" for="fieldId">问题ID</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldTitle" checked>
                                                    <label class="form-check-label" for="fieldTitle">问题标题</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldDescription" checked>
                                                    <label class="form-check-label" for="fieldDescription">问题描述</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldStatus" checked>
                                                    <label class="form-check-label" for="fieldStatus">状态</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <h6>人员信息</h6>
                                            <div class="field-group">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldCreator" checked>
                                                    <label class="form-check-label" for="fieldCreator">创建人</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldAssignee" checked>
                                                    <label class="form-check-label" for="fieldAssignee">分配给</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldManager" checked>
                                                    <label class="form-check-label" for="fieldManager">负责人</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldTeam">
                                                    <label class="form-check-label" for="fieldTeam">产品线</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <h6>时间信息</h6>
                                            <div class="field-group">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldCreateTime" checked>
                                                    <label class="form-check-label" for="fieldCreateTime">创建时间</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldResolveTime">
                                                    <label class="form-check-label" for="fieldResolveTime">解决时间</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <h6>其他信息</h6>
                                            <div class="field-group">
                                                <!-- 优先级和分类暂未设计，先注释掉 -->
                                                <!--
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldPriority">
                                                    <label class="form-check-label" for="fieldPriority">优先级</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldCategory">
                                                    <label class="form-check-label" for="fieldCategory">分类</label>
                                                </div>
                                                -->
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="fieldAttachments">
                                                    <label class="form-check-label" for="fieldAttachments">附件数量</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 字段选择快捷操作 -->
                                    <div class="field-actions mt-3 d-flex gap-3 flex-wrap justify-content-center">
                                        <button class="btn-modern btn-primary btn-lg" onclick="selectAllFields()">
                                            <i class="fas fa-check-square me-2"></i>全选
                                        </button>
                                        <button class="btn-modern btn-secondary btn-lg" onclick="selectNoneFields()">
                                            <i class="fas fa-square me-2"></i>全不选
                                        </button>
                                        <button class="btn-modern btn-info btn-lg" onclick="selectBasicFields()">
                                            <i class="fas fa-list me-2"></i>基本字段
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 导出格式和操作 -->
                        <div class="export-section">
                            <div class="export-header">
                                <h3><i class="fas fa-download"></i> 导出操作</h3>
                            </div>
                            <div class="export-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">导出格式</label>
                                            <div class="format-options">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="exportFormat" id="formatExcel" value="excel" checked>
                                                    <label class="form-check-label" for="formatExcel">
                                                        <i class="fas fa-file-excel text-success"></i> Excel (.xlsx)
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">文件名</label>
                                            <input type="text" class="form-control" id="exportFileName" placeholder="问题列表报表">
                                            <small class="text-muted">不需要包含文件扩展名</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 导出按钮 -->
                                <div class="export-actions mt-4">
                                    <button class="btn-modern btn-primary btn-lg me-3" onclick="previewExport()">
                                        <i class="fas fa-eye me-2"></i>
                                        预览数据
                                    </button>
                                    <button class="btn-modern btn-success btn-lg" onclick="exportData()">
                                        <i class="fas fa-download me-2"></i>
                                        导出数据
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 图表展示区域 -->
                        <div class="export-section" id="chartsSection" style="display: none;">
                            <div class="export-header">
                                <h3><i class="fas fa-chart-bar"></i> 数据统计图表</h3>
                            </div>
                            <div class="export-body">
                                <!-- 图表切换按钮 -->
                                <div class="chart-controls mb-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0"><i class="fas fa-user-edit"></i> 提交人统计（已完成）</h5>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="exportChart('creator')" title="导出图表">
                                                    <i class="fas fa-download"></i> 导出
                                                </button>
                                            </div>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-primary active" onclick="switchChart('creator', 'line')">
                                                    <i class="fas fa-chart-line"></i> 折线图
                                                </button>
                                                <button type="button" class="btn btn-outline-primary" onclick="switchChart('creator', 'bar')">
                                                    <i class="fas fa-chart-bar"></i> 柱状图
                                                </button>
                                                <button type="button" class="btn btn-outline-primary" onclick="switchChart('creator', 'pie')">
                                                    <i class="fas fa-chart-pie"></i> 饼状图
                                                </button>
                                            </div>
                                            <div class="chart-container mt-3">
                                                <canvas id="creatorChart" width="400" height="300"></canvas>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0"><i class="fas fa-user-check"></i> 处理人统计（已完成）</h5>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="exportChart('assignee')" title="导出图表">
                                                    <i class="fas fa-download"></i> 导出
                                                </button>
                                            </div>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-success active" onclick="switchChart('assignee', 'line')">
                                                    <i class="fas fa-chart-line"></i> 折线图
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="switchChart('assignee', 'bar')">
                                                    <i class="fas fa-chart-bar"></i> 柱状图
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="switchChart('assignee', 'pie')">
                                                    <i class="fas fa-chart-pie"></i> 饼状图
                                                </button>
                                            </div>
                                            <div class="chart-container mt-3">
                                                <canvas id="assigneeChart" width="400" height="300"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 预览区域 -->
                        <div class="export-section" id="previewSection" style="display: none;">
                            <div class="export-header">
                                <h3><i class="fas fa-table"></i> 数据预览</h3>
                            </div>
                            <div class="export-body">
                                <div class="preview-info">
                                    <span class="badge bg-info" id="previewCount">0 条记录</span>
                                    <span class="badge bg-secondary" id="previewFields">0 个字段</span>
                                </div>
                                <div class="table-responsive mt-3">
                                    <table class="table table-striped table-hover" id="previewTable">
                                        <thead id="previewTableHead"></thead>
                                        <tbody id="previewTableBody"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit"></i>
                    编辑用户信息
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 30px;">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsername" name="username" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editChineseName" class="form-label">中文名</label>
                            <input type="text" class="form-control" id="editChineseName" name="chinese_name">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="editEmail" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPhone" class="form-label">电话</label>
                            <input type="tel" class="form-control" id="editPhone" name="phone">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editRole" class="form-label">角色</label>
                            <select class="form-select" id="editRole" name="role" required>
                                <option value="管理员">管理员</option>
                                <option value="负责人">负责人</option>
                                <option value="实施组">实施组</option>
                                <option value="组内成员">组内成员</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editTeam" class="form-label">团队</label>
                            <select class="form-select" id="editTeam" name="team">
                                <option value="">请选择团队</option>
                                <option value="网络分析">网络分析</option>
                                <option value="实施组">实施组</option>
                                <option value="第三道防线">第三道防线</option>
                                <option value="新能源">新能源</option>
                                <option value="管理员">管理员</option>
                                <option value="开发组">开发组</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editPassword" class="form-label">新密码（留空则不修改）</label>
                        <input type="password" class="form-control" id="editPassword" name="password" placeholder="留空则不修改密码">
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border: none; padding: 20px 30px;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveUserChanges()">
                    <i class="fas fa-save"></i>
                    保存更改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 现代化确认删除Modal -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header confirm-delete-header">
                <div class="confirm-delete-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h5 class="modal-title">确认删除</h5>
            </div>
            <div class="modal-body confirm-delete-body">
                <div class="warning-animation">
                    <div class="warning-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                </div>
                <p class="confirm-delete-message">确定要删除这个用户吗？</p>
                <p class="warning-text">此操作不可撤销！</p>
            </div>
            <div class="modal-footer confirm-delete-footer">
                <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn-modern btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 现代化成功提示Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header success-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h5 class="modal-title" id="successModalTitle">操作成功！</h5>
            </div>
            <div class="modal-body success-body">
                <div class="success-animation">
                    <div class="checkmark">
                        <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                        </svg>
                    </div>
                </div>
                <p class="success-message" id="successModalMessage">操作已成功完成</p>
            </div>
            <div class="modal-footer success-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 现代化错误提示Modal -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header error-header">
                <div class="error-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h5 class="modal-title">操作失败</h5>
            </div>
            <div class="modal-body error-body">
                <div class="error-animation">
                    <div class="error-icon-large">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <p class="error-message" id="errorModalMessage">操作失败，请稍后重试</p>
            </div>
            <div class="modal-footer error-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出成功Modal -->
<div class="modal fade" id="exportSuccessModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header success-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h5 class="modal-title">导出成功！</h5>
            </div>
            <div class="modal-body success-body">
                <div class="success-animation">
                    <div class="checkmark">
                        <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                        </svg>
                    </div>
                </div>
                <p class="success-message" id="exportSuccessMessage">Excel文件已成功生成并开始下载</p>
            </div>
            <div class="modal-footer success-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出失败Modal -->
<div class="modal fade" id="exportErrorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header error-header">
                <div class="error-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h5 class="modal-title">导出失败</h5>
            </div>
            <div class="modal-body error-body">
                <div class="error-animation">
                    <div class="error-icon-large">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <p class="error-message" id="exportErrorMessage">导出失败，请稍后重试</p>
            </div>
            <div class="modal-footer error-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 统计数据计算
function updateStats() {
    const allBugs = document.querySelectorAll('#bugsTableBody tr');
    const totalCount = allBugs.length;

    let pendingCount = 0;
    let assignedCount = 0;
    let processingCount = 0;
    let resolvedCount = 0;
    let completedCount = 0;

    allBugs.forEach(bug => {
        const status = bug.dataset.status;
        if (status === '待处理') {
            pendingCount++;
        } else if (status === '已分配') {
            assignedCount++;
        } else if (status === '处理中') {
            processingCount++;
        } else if (status === '已解决') {
            resolvedCount++;
        } else if (status === '已完成') {
            completedCount++;
        }
    });

    document.getElementById('totalBugs').textContent = totalCount;
    document.getElementById('pendingBugs').textContent = pendingCount;
    document.getElementById('assignedBugs').textContent = assignedCount;
    document.getElementById('processingBugs').textContent = processingCount;
    document.getElementById('resolvedBugs').textContent = resolvedCount;
    document.getElementById('completedBugs').textContent = completedCount;
}

// 选项卡切换功能
function switchTab(tabName) {
    // 移除所有选项卡的激活状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // 激活当前选项卡
    document.getElementById(tabName + 'Tab').classList.add('active');
    document.getElementById(tabName + 'Pane').classList.add('active');

    // 如果切换到问题管理，更新统计
    if (tabName === 'bugs') {
        updateStats();
    } else if (tabName === 'notifications') {
        loadNotificationConfig();
    } else if (tabName === 'reports') {
        initializeReportsTab();
    }
}

// 按状态筛选
function filterByStatus(status) {
    // 更新卡片激活状态
    document.querySelectorAll('.stat-card-inline').forEach(card => {
        card.classList.remove('active');
    });

    if (status === 'all') {
        document.querySelector('[data-status="all"]').classList.add('active');
        // 显示所有行
        document.querySelectorAll('#bugsTableBody tr').forEach(row => {
            row.style.display = '';
        });
        document.querySelectorAll('#usersTableBody tr').forEach(row => {
            row.style.display = '';
        });
    } else if (status === 'bugs') {
        document.querySelector('[data-status="bugs"]').classList.add('active');
        // 切换到问题管理选项卡
        switchTab('bugs');
    } else {
        document.querySelector(`[data-status="${status}"]`).classList.add('active');
        // 切换到问题管理选项卡并筛选
        switchTab('bugs');
        setTimeout(() => {
            // 筛选问题
            document.querySelectorAll('#bugsTableBody tr').forEach(row => {
                const bugStatus = row.dataset.status;
                if (status === 'pending' && bugStatus === '待处理') {
                    row.style.display = '';
                } else if (status === 'assigned' && bugStatus === '已分配') {
                    row.style.display = '';
                } else if (status === 'processing' && bugStatus === '处理中') {
                    row.style.display = '';
                } else if (status === 'resolved' && bugStatus === '已解决') {
                    row.style.display = '';
                } else if (status === 'completed' && bugStatus === '已完成') {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            updateVisibleBugsCount();
        }, 100);
    }
}

// 全局变量
let notificationDropdownOpen = false;

// 通知中心位置调整
function adjustNotificationPosition() {
    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) return;

    console.log('Adjusting notification position...');

    const buttonRect = button.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 强制使用固定定位，确保不被遮挡
    dropdown.style.position = 'fixed';
    dropdown.style.zIndex = '99999';

    // 检查是否是移动端
    if (viewportWidth <= 768) {
        // 移动端居中显示
        dropdown.style.top = '50%';
        dropdown.style.left = '50%';
        dropdown.style.transform = 'translate(-50%, -50%)';
        dropdown.style.right = 'auto';
        dropdown.style.bottom = 'auto';
        dropdown.style.width = '90vw';
        dropdown.style.maxWidth = '400px';
    } else {
        // 桌面端定位在按钮附近
        dropdown.style.transform = 'none';
        dropdown.style.width = 'auto';

        // 计算最佳位置
        const spaceBelow = viewportHeight - buttonRect.bottom;
        const spaceAbove = buttonRect.top;
        const dropdownHeight = dropdown.offsetHeight || 400;

        if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
            // 显示在按钮下方
            dropdown.style.top = (buttonRect.bottom + 10) + 'px';
            dropdown.style.bottom = 'auto';
        } else {
            // 显示在按钮上方
            dropdown.style.top = 'auto';
            dropdown.style.bottom = (viewportHeight - buttonRect.top + 10) + 'px';
        }

        // 水平定位 - 右对齐到按钮
        const dropdownWidth = dropdown.offsetWidth || 380;
        if (buttonRect.right >= dropdownWidth) {
            dropdown.style.right = (viewportWidth - buttonRect.right) + 'px';
            dropdown.style.left = 'auto';
        } else {
            dropdown.style.left = buttonRect.left + 'px';
            dropdown.style.right = 'auto';
        }
    }

    console.log('Position adjusted:', {
        position: dropdown.style.position,
        top: dropdown.style.top,
        right: dropdown.style.right,
        left: dropdown.style.left,
        zIndex: dropdown.style.zIndex,
        transform: dropdown.style.transform
    });
}

// 切换通知下拉菜单
function toggleNotificationDropdown(event) {
    // 强制阻止所有事件传播
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    console.log('toggleNotificationDropdown called');

    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) {
        console.log('Elements not found');
        return false;
    }

    const computedStyle = window.getComputedStyle(dropdown);
    const isCurrentlyVisible = computedStyle.display !== 'none';

    if (isCurrentlyVisible) {
        // 关闭下拉菜单
        console.log('Closing dropdown');
        dropdown.style.display = 'none';
        button.setAttribute('aria-expanded', 'false');
        notificationDropdownOpen = false;
    } else {
        // 打开下拉菜单
        console.log('Opening dropdown');
        dropdown.style.display = 'block';
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        dropdown.style.zIndex = '10000';
        button.setAttribute('aria-expanded', 'true');
        notificationDropdownOpen = true;

        // 先调整位置，再检查样式
        setTimeout(function() {
            adjustNotificationPosition();

            // 检查样式是否生效
            const newStyle = window.getComputedStyle(dropdown);
            const rect = dropdown.getBoundingClientRect();
            console.log('Dropdown after opening:', {
                display: newStyle.display,
                visibility: newStyle.visibility,
                opacity: newStyle.opacity,
                zIndex: newStyle.zIndex,
                position: newStyle.position,
                rect: rect,
                isVisible: rect.width > 0 && rect.height > 0
            });
        }, 50);
    }

    return false;
}

// 标记所有通知为已读
function markAllAsRead(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // 移除所有未读状态
    const unreadItems = document.querySelectorAll('.notification-item.unread');
    unreadItems.forEach(item => {
        item.classList.remove('unread');
        const dot = item.querySelector('.notification-dot');
        if (dot) {
            dot.remove();
        }
    });

    // 更新徽章
    const badge = document.getElementById('notificationBadge');
    if (badge) {
        badge.textContent = '0';
        badge.style.display = 'none';
    }
}

// 更新可见问题数量
function updateVisibleBugsCount() {
    const visibleRows = document.querySelectorAll('#bugsTableBody tr[style=""], #bugsTableBody tr:not([style*="none"])');
    const visibleCount = Array.from(visibleRows).filter(row =>
        !row.style.display || row.style.display !== 'none'
    ).length;
    document.getElementById('visibleBugsCount').textContent = visibleCount;
}

// 筛选功能（通过复选框）
function updateBugsList() {
    const checkboxes = document.querySelectorAll('.status-checkbox');
    const selectedStatuses = Array.from(checkboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

    const bugRows = document.querySelectorAll('#bugsTableBody tr');
    let visibleCount = 0;

    bugRows.forEach(row => {
        const bugStatus = row.dataset.status;
        if (selectedStatuses.includes('all') || selectedStatuses.includes(bugStatus)) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    document.getElementById('visibleBugsCount').textContent = visibleCount;
}

// 编辑用户
async function editUser(userId) {
    try {
        // 获取用户数据
        const response = await fetch(`/admin/users/${userId}`);
        if (!response.ok) {
            throw new Error('获取用户数据失败');
        }

        const user = await response.json();

        // 填充表单
        document.getElementById('editUserId').value = user.id;
        document.getElementById('editUsername').value = user.username || '';
        document.getElementById('editChineseName').value = user.chinese_name || '';
        document.getElementById('editEmail').value = user.email || '';
        document.getElementById('editPhone').value = user.phone || '';
        document.getElementById('editRole').value = user.role || '';
        document.getElementById('editTeam').value = user.team || '';
        document.getElementById('editPassword').value = '';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();

    } catch (error) {
        console.error('编辑用户错误:', error);
        alert('获取用户数据失败：' + error.message);
    }
}

// 保存用户更改
async function saveUserChanges() {
    try {
        const userId = document.getElementById('editUserId').value;
        const formData = new FormData(document.getElementById('editUserForm'));

        // 转换为JSON对象
        const userData = {};
        for (let [key, value] of formData.entries()) {
            if (key !== 'user_id' && value.trim() !== '') {
                userData[key] = value.trim();
            }
        }

        // 如果密码为空，不包含在更新数据中
        if (!userData.password) {
            delete userData.password;
        }

        const response = await fetch(`/admin/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (result.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            modal.hide();

            // 刷新页面
            location.reload();
        } else {
            document.getElementById('errorModalMessage').textContent = '更新失败：' + result.message;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }

    } catch (error) {
        console.error('保存用户更改错误:', error);
        document.getElementById('errorModalMessage').textContent = '保存失败：' + error.message;
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    }
}

// 删除用户
let currentDeleteUserId = null;

function deleteUser(userId) {
    currentDeleteUserId = userId;

    // 显示确认删除Modal
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

// 确认删除用户
async function confirmDeleteUser() {
    if (!currentDeleteUserId) return;

    try {
        const response = await fetch(`/admin/users/${currentDeleteUserId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        // 关闭确认Modal
        const confirmModal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
        confirmModal.hide();

        if (result.success) {
            // 显示成功Modal
            document.getElementById('successModalTitle').textContent = '删除成功！';
            document.getElementById('successModalMessage').textContent = '用户已成功删除';
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();

            // 2秒后刷新页面
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            // 显示错误Modal
            document.getElementById('errorModalMessage').textContent = '删除失败：' + result.message;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }

    } catch (error) {
        console.error('删除用户错误:', error);

        // 关闭确认Modal
        const confirmModal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
        if (confirmModal) confirmModal.hide();

        // 显示错误Modal
        document.getElementById('errorModalMessage').textContent = '删除失败：' + error.message;
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    }

    currentDeleteUserId = null;
}

function deleteBugAdmin(bugId) {
    // 使用现有的确认删除Modal
    currentDeleteUserId = null; // 清除用户删除状态

    // 修改Modal内容为删除问题
    document.querySelector('#confirmDeleteModal .modal-title').textContent = '确认删除问题';
    document.querySelector('#confirmDeleteModal .confirm-delete-message').textContent = '确定要删除这个问题吗？';
    document.querySelector('#confirmDeleteModal .warning-text').textContent = '此操作不可撤销！';

    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();

    // 重新绑定确认按钮事件
    document.getElementById('confirmDeleteBtn').onclick = function() {
        modal.hide();

        fetch(`/bug/delete/${bugId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('successModalTitle').textContent = '删除成功！';
                document.getElementById('successModalMessage').textContent = '问题已成功删除';
                const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                successModal.show();

                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                document.getElementById('errorModalMessage').textContent = '删除失败：' + data.message;
                const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                errorModal.show();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('errorModalMessage').textContent = '删除失败，请重试';
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        });
    };
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStats();

    // 默认激活"总用户数"卡片
    document.querySelector('[data-status="all"]').classList.add('active');

    // 绑定筛选事件
    document.querySelectorAll('.status-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBugsList);
    });

    // 统计卡片动画
    const statCards = document.querySelectorAll('.stat-card-inline');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.animation = 'fadeInUp 0.6s ease-out forwards';
    });

    // 点击外部关闭通知下拉菜单
    document.addEventListener('click', function(event) {
        const notificationCenter = document.querySelector('.notification-center');
        const dropdown = document.getElementById('notificationDropdownMenu');

        if (!notificationCenter.contains(event.target)) {
            dropdown.style.display = 'none';
        }
    });

    // 绑定确认删除按钮事件
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', confirmDeleteUser);
    }
});

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// 监听通知按钮点击事件
document.addEventListener('DOMContentLoaded', function() {
    const notificationButton = document.querySelector('#notificationDropdown');
    const dropdown = document.querySelector('.notification-dropdown');

    if (notificationButton && dropdown) {
        // 完全移除Bootstrap的下拉菜单功能
        if (window.bootstrap && window.bootstrap.Dropdown) {
            const existingDropdown = window.bootstrap.Dropdown.getInstance(notificationButton);
            if (existingDropdown) {
                existingDropdown.dispose();
            }
        }

        // 阻止Bootstrap的下拉菜单初始化
        notificationButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (notificationDropdownOpen) {
                adjustNotificationPosition();
            }
        });

        // 点击外部区域关闭通知中心
        document.addEventListener('click', function(event) {
            const newButton = document.querySelector('#notificationDropdown');
            const computedStyle = window.getComputedStyle(dropdown);
            const isCurrentlyVisible = computedStyle.display !== 'none';
            if (isCurrentlyVisible) {
                if (!newButton.contains(event.target) && !dropdown.contains(event.target)) {
                    dropdown.style.display = 'none';
                    newButton.setAttribute('aria-expanded', 'false');
                    notificationDropdownOpen = false;
                }
            }
        });
    }
});

// 通知配置相关函数
async function loadNotificationConfig() {
    console.log('加载通知配置...');

    try {
        const response = await fetch('/admin/notifications/config');
        const config = await response.json();

        if (config.success) {
            const data = config.data;

            // 全局设置
            if (data.server) {
                document.getElementById('enableServerNotifications').checked = data.server.enabled !== false;
                document.getElementById('notificationRetention').value = data.server.retention_days || 30;
                document.getElementById('enableAutoCleanup').checked = data.server.auto_cleanup_enabled !== false;
            }

            // 应用内通知
            if (data.inapp) {
                document.getElementById('enableInAppNotifications').checked = data.inapp.enabled !== false;
                document.getElementById('maxNotificationsPerUser').value = data.inapp.max_notifications_per_user || 100;
            }

            // 邮件通知
            if (data.email) {
                document.getElementById('enableEmailNotifications').checked = data.email.enabled === true;
                document.getElementById('smtpServer').value = data.email.smtp_server || 'smtp.gmail.com';
                document.getElementById('smtpPort').value = data.email.smtp_port || 587;
                document.getElementById('smtpUsername').value = data.email.smtp_username || '';
                document.getElementById('smtpPassword').value = data.email.smtp_password || '';
                document.getElementById('fromEmail').value = data.email.from_email || '<EMAIL>';
                document.getElementById('fromName').value = data.email.from_name || 'ReBugTracker';
                document.getElementById('smtpUseTls').checked = data.email.use_tls !== false;
            }

            // Gotify通知
            if (data.gotify) {
                document.getElementById('enableGotifyNotifications').checked = data.gotify.enabled === true;
                document.getElementById('gotifyServerUrl').value = data.gotify.server_url || 'http://localhost:8080';
                document.getElementById('gotifyAppToken').value = data.gotify.app_token || '';
                document.getElementById('gotifyDefaultPriority').value = data.gotify.default_priority || 5;
            }

            // 通知类型
            if (data.flow_rules) {
                document.getElementById('notifyBugCreated').checked = data.flow_rules.bug_created !== false;
                document.getElementById('notifyBugAssigned').checked = data.flow_rules.bug_assigned !== false;
                document.getElementById('notifyBugStatusChanged').checked = data.flow_rules.bug_status_changed !== false;
                document.getElementById('notifyBugResolved').checked = data.flow_rules.bug_resolved !== false;
                document.getElementById('notifyBugClosed').checked = data.flow_rules.bug_closed !== false;
            }

            console.log('通知配置加载成功');
        } else {
            console.warn('加载通知配置失败，使用默认配置');
        }

        // 同时加载用户通知偏好
        await loadUserNotificationPreferences();

        // 加载清理统计信息
        await loadCleanupStats();

    } catch (error) {
        console.error('加载通知配置错误:', error);
        console.log('使用默认配置');
    }
}

async function saveNotificationConfig() {
    const config = {
        // 全局设置
        server: {
            enabled: document.getElementById('enableServerNotifications').checked,
            retention_days: parseInt(document.getElementById('notificationRetention').value),
            auto_cleanup_enabled: document.getElementById('enableAutoCleanup').checked
        },

        // 应用内通知
        inapp: {
            enabled: document.getElementById('enableInAppNotifications').checked,
            max_notifications_per_user: parseInt(document.getElementById('maxNotificationsPerUser').value)
        },

        // 邮件通知
        email: {
            enabled: document.getElementById('enableEmailNotifications').checked,
            smtp_server: document.getElementById('smtpServer').value,
            smtp_port: parseInt(document.getElementById('smtpPort').value),
            smtp_username: document.getElementById('smtpUsername').value,
            smtp_password: document.getElementById('smtpPassword').value,
            from_email: document.getElementById('fromEmail').value,
            from_name: document.getElementById('fromName').value,
            use_tls: document.getElementById('smtpUseTls').checked
        },

        // Gotify通知
        gotify: {
            enabled: document.getElementById('enableGotifyNotifications').checked,
            server_url: document.getElementById('gotifyServerUrl').value,
            app_token: document.getElementById('gotifyAppToken').value,
            default_priority: parseInt(document.getElementById('gotifyDefaultPriority').value)
        },

        // 通知类型
        flow_rules: {
            bug_created: document.getElementById('notifyBugCreated').checked,
            bug_assigned: document.getElementById('notifyBugAssigned').checked,
            bug_status_changed: document.getElementById('notifyBugStatusChanged').checked,
            bug_resolved: document.getElementById('notifyBugResolved').checked,
            bug_closed: document.getElementById('notifyBugClosed').checked
        }
    };

    console.log('保存通知配置:', config);

    try {
        // 发送到后端API
        const response = await fetch('/admin/notifications/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        });

        const result = await response.json();

        if (result.success) {
            return { success: true, message: '全局配置保存成功' };
        } else {
            return { success: false, message: '全局配置保存失败：' + (result.message || '未知错误') };
        }
    } catch (error) {
        console.error('保存通知配置错误:', error);
        return { success: false, message: '全局配置保存失败：网络错误' };
    }
}

// 新的统一保存函数
async function saveAllNotificationConfig() {
    console.log('开始保存所有通知配置...');

    // 显示加载状态
    const saveButton = document.querySelector('.config-actions button');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    saveButton.disabled = true;

    try {
        // 1. 保存全局配置
        console.log('保存全局配置...');
        const globalResult = await saveNotificationConfig();

        // 2. 保存所有用户偏好
        console.log('保存用户偏好...');
        const userResult = await saveAllUserPreferences();

        // 3. 显示结果
        if (globalResult.success && userResult.success) {
            // 显示成功Modal
            document.getElementById('successModalTitle').textContent = '保存成功！';
            document.getElementById('successModalMessage').textContent = '全局配置和用户偏好已全部保存成功';
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
        } else {
            // 显示错误信息
            let errorMessage = '保存过程中出现问题：\n';
            if (!globalResult.success) {
                errorMessage += '• ' + globalResult.message + '\n';
            }
            if (!userResult.success) {
                errorMessage += '• ' + userResult.message + '\n';
            }

            document.getElementById('errorModalMessage').textContent = errorMessage;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }

    } catch (error) {
        console.error('保存配置时发生错误:', error);
        document.getElementById('errorModalMessage').textContent = '保存失败：' + error.message;
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    } finally {
        // 恢复按钮状态
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    }
}

async function testNotification() {
    console.log('测试通知...');

    try {
        const response = await fetch('/admin/notifications/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                channels: ['inapp', 'email', 'gotify'],
                message: '这是一条测试通知，用于验证通知系统是否正常工作。'
            })
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('successModalTitle').textContent = '测试通知已发送！';
            document.getElementById('successModalMessage').textContent = `成功渠道：${result.successful_channels.join(', ')}\n失败渠道：${result.failed_channels.join(', ')}`;
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
        } else {
            document.getElementById('errorModalMessage').textContent = '测试通知发送失败：' + (result.message || '未知错误');
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }
    } catch (error) {
        console.error('测试通知错误:', error);
        document.getElementById('errorModalMessage').textContent = '测试通知发送失败：网络错误';
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    }
}

function resetNotificationConfig() {
    if (confirm('确定要重置所有通知配置吗？此操作将恢复默认设置。')) {
        // 重置为默认值
        // 全局设置
        document.getElementById('enableServerNotifications').checked = true;
        document.getElementById('notificationRetention').value = '30';
        document.getElementById('enableAutoCleanup').checked = false;

        // 应用内通知
        document.getElementById('enableInAppNotifications').checked = true;
        document.getElementById('maxNotificationsPerUser').value = '100';

        // 邮件通知
        document.getElementById('enableEmailNotifications').checked = false;
        document.getElementById('smtpServer').value = 'smtp.gmail.com';
        document.getElementById('smtpPort').value = '587';
        document.getElementById('smtpUsername').value = '';
        document.getElementById('smtpPassword').value = '';
        document.getElementById('fromEmail').value = '<EMAIL>';
        document.getElementById('fromName').value = 'ReBugTracker';
        document.getElementById('smtpUseTls').checked = true;

        // Gotify通知
        document.getElementById('enableGotifyNotifications').checked = false;
        document.getElementById('gotifyServerUrl').value = 'http://localhost:8080';
        document.getElementById('gotifyAppToken').value = '';
        document.getElementById('gotifyDefaultPriority').value = '5';

        // 通知类型
        document.getElementById('notifyBugCreated').checked = true;
        document.getElementById('notifyBugAssigned').checked = true;
        document.getElementById('notifyBugStatusChanged').checked = true;
        document.getElementById('notifyBugResolved').checked = true;
        document.getElementById('notifyBugClosed').checked = true;

        console.log('通知配置已重置为默认值');
        alert('通知配置已重置为默认设置！');
    }
}

// 用户通知偏好相关函数
async function loadUserNotificationPreferences() {
    console.log('加载用户通知偏好...');

    try {
        const response = await fetch('/admin/notifications/user-preferences');
        const result = await response.json();

        if (result.success) {
            const preferences = result.data;

            // 更新每个用户的通知开关状态
            Object.keys(preferences).forEach(userId => {
                const userPref = preferences[userId];

                // 更新应用内通知开关
                const inappSwitch = document.getElementById(`inapp_${userId}`);
                if (inappSwitch) {
                    inappSwitch.checked = userPref.inapp !== false;
                }

                // 更新邮件通知开关
                const emailSwitch = document.getElementById(`email_${userId}`);
                if (emailSwitch) {
                    emailSwitch.checked = userPref.email !== false;
                }

                // 更新Gotify通知开关
                const gotifySwitch = document.getElementById(`gotify_${userId}`);
                if (gotifySwitch) {
                    gotifySwitch.checked = userPref.gotify !== false;
                }
            });

            console.log('用户通知偏好加载成功');
        } else {
            console.warn('加载用户通知偏好失败，使用默认设置');
        }
    } catch (error) {
        console.error('加载用户通知偏好错误:', error);
        console.log('使用默认设置');
    }
}

async function saveUserNotificationPreference(userId) {
    console.log(`保存用户 ${userId} 的通知偏好...`);

    try {
        const inappEnabled = document.getElementById(`inapp_${userId}`).checked;
        const emailEnabled = document.getElementById(`email_${userId}`).checked;
        const gotifyEnabled = document.getElementById(`gotify_${userId}`).checked;

        const preferences = {
            user_id: userId,
            inapp: inappEnabled,
            email: emailEnabled,
            gotify: gotifyEnabled
        };

        const response = await fetch('/admin/notifications/user-preferences', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(preferences)
        });

        const result = await response.json();

        if (result.success) {
            // 显示成功提示
            const row = document.querySelector(`tr[data-user-id="${userId}"]`);
            const saveBtn = row.querySelector('button');
            const originalText = saveBtn.innerHTML;

            saveBtn.innerHTML = '<i class="fas fa-check"></i> 已保存';
            saveBtn.classList.remove('btn-outline-primary');
            saveBtn.classList.add('btn-success');

            setTimeout(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.classList.remove('btn-success');
                saveBtn.classList.add('btn-outline-primary');
            }, 2000);

        } else {
            alert('保存失败：' + (result.message || '未知错误'));
        }
    } catch (error) {
        console.error('保存用户通知偏好错误:', error);
        alert('保存失败：网络错误');
    }
}

async function saveAllUserPreferences() {
    console.log('保存所有用户通知偏好...');

    try {
        const allPreferences = [];
        const userRows = document.querySelectorAll('#userNotificationPreferences tr[data-user-id]');

        userRows.forEach(row => {
            const userId = row.dataset.userId;
            const inappEnabled = document.getElementById(`inapp_${userId}`).checked;
            const emailEnabled = document.getElementById(`email_${userId}`).checked;
            const gotifyEnabled = document.getElementById(`gotify_${userId}`).checked;

            allPreferences.push({
                user_id: userId,
                inapp: inappEnabled,
                email: emailEnabled,
                gotify: gotifyEnabled
            });
        });

        const response = await fetch('/admin/notifications/user-preferences/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ preferences: allPreferences })
        });

        const result = await response.json();

        if (result.success) {
            return { success: true, message: `成功保存 ${result.updated_count} 个用户的通知偏好` };
        } else {
            return { success: false, message: '批量保存失败：' + (result.message || '未知错误') };
        }
    } catch (error) {
        console.error('批量保存用户通知偏好错误:', error);
        return { success: false, message: '批量保存失败：网络错误' };
    }
}

function batchToggleNotification(channel, enabled) {
    console.log(`批量${enabled ? '启用' : '禁用'}${channel}通知...`);

    const userRows = document.querySelectorAll('#userNotificationPreferences tr[data-user-id]');

    userRows.forEach(row => {
        const userId = row.dataset.userId;
        const switchElement = document.getElementById(`${channel}_${userId}`);
        if (switchElement) {
            switchElement.checked = enabled;
        }
    });

    const action = enabled ? '启用' : '禁用';
    const channelName = {
        'inapp': '应用内',
        'email': '邮件',
        'gotify': 'Gotify'
    }[channel];

    alert(`已${action}所有用户的${channelName}通知！请点击"保存所有用户偏好"来保存更改。`);
}

// 报表导出相关函数
function initializeReportsTab() {
    console.log('初始化报表导出选项卡...');

    // 设置默认日期范围为本月
    setDateRange('month');

    // 设置默认文件名
    const today = new Date();
    const defaultFileName = `问题列表报表_${today.getFullYear()}${(today.getMonth()+1).toString().padStart(2,'0')}${today.getDate().toString().padStart(2,'0')}`;
    document.getElementById('exportFileName').value = defaultFileName;

    // 绑定筛选条件变化事件
    document.getElementById('filterByStatus').addEventListener('change', function() {
        const statusFilters = document.getElementById('statusFilters');
        statusFilters.style.display = this.checked ? 'block' : 'none';
    });
}

function setDateRange(range) {
    const today = new Date();
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    // 移除所有按钮的激活状态
    const quickDateButtons = document.querySelectorAll('.quick-dates .btn');
    quickDateButtons.forEach(btn => btn.classList.remove('active'));

    // 设置当前点击按钮为激活状态
    const currentButton = event.target.closest('button');
    if (currentButton) {
        currentButton.classList.add('active');
    }

    // 设置结束日期为今天
    endDate.value = today.toISOString().split('T')[0];

    let startDateValue;
    switch(range) {
        case 'week':
            // 本周：从本周一开始
            startDateValue = new Date(today);
            const dayOfWeek = today.getDay();
            const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 周日为0，需要特殊处理
            startDateValue.setDate(today.getDate() - daysToMonday);
            break;
        case 'month':
            // 本月：从本月1号开始
            startDateValue = new Date(today.getFullYear(), today.getMonth(), 1);
            break;
        case 'year':
            // 本年：从今年1月1号开始
            startDateValue = new Date(today.getFullYear(), 0, 1);
            break;
        case 'all':
            startDate.value = '';
            endDate.value = '';
            return;
        default:
            return;
    }

    startDate.value = startDateValue.toISOString().split('T')[0];
}

function selectAllFields() {
    const checkboxes = document.querySelectorAll('.field-group input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function selectNoneFields() {
    const checkboxes = document.querySelectorAll('.field-group input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

function selectBasicFields() {
    // 先全部取消选择
    selectNoneFields();

    // 选择基本字段
    const basicFields = ['fieldId', 'fieldTitle', 'fieldStatus', 'fieldCreator', 'fieldCreateTime'];
    basicFields.forEach(fieldId => {
        const checkbox = document.getElementById(fieldId);
        if (checkbox) checkbox.checked = true;
    });
}

async function previewExport() {
    console.log('预览导出数据...');

    try {
        const exportConfig = getExportConfig();

        const response = await fetch('/admin/reports/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exportConfig)
        });

        const result = await response.json();

        if (result.success) {
            displayPreview(result.data, result.fields);
            // 同时加载图表数据
            loadChartData();
        } else {
            alert('预览失败：' + (result.message || '未知错误'));
        }
    } catch (error) {
        console.error('预览数据错误:', error);
        alert('预览失败：网络错误');
    }
}

function displayPreview(data, fields) {
    const previewSection = document.getElementById('previewSection');
    const previewCount = document.getElementById('previewCount');
    const previewFields = document.getElementById('previewFields');
    const previewTableHead = document.getElementById('previewTableHead');
    const previewTableBody = document.getElementById('previewTableBody');

    // 更新统计信息
    previewCount.textContent = `${data.length} 条记录`;
    previewFields.textContent = `${fields.length} 个字段`;

    // 生成表头
    previewTableHead.innerHTML = '<tr>' + fields.map(field => `<th>${field.label}</th>`).join('') + '</tr>';

    // 生成表格内容（最多显示前10行）
    const displayData = data.slice(0, 10);
    previewTableBody.innerHTML = displayData.map(row =>
        '<tr>' + fields.map(field => `<td>${row[field.key] || '-'}</td>`).join('') + '</tr>'
    ).join('');

    // 如果数据超过10行，添加提示
    if (data.length > 10) {
        previewTableBody.innerHTML += `<tr><td colspan="${fields.length}" class="text-center text-muted"><em>... 还有 ${data.length - 10} 条记录</em></td></tr>`;
    }

    // 显示预览区域
    previewSection.style.display = 'block';
    previewSection.scrollIntoView({ behavior: 'smooth' });
}

async function exportData() {
    console.log('导出数据...');

    try {
        const exportConfig = getExportConfig();

        const response = await fetch('/admin/reports/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exportConfig)
        });

        if (response.ok) {
            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = exportConfig.filename || '问题列表报表';
            if (contentDisposition) {
                // 优先解析UTF-8编码的文件名
                const utf8Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
                if (utf8Match) {
                    try {
                        filename = decodeURIComponent(utf8Match[1]);
                    } catch (e) {
                        console.warn('UTF-8文件名解码失败:', e);
                    }
                } else {
                    // 兜底使用ASCII文件名
                    const asciiMatch = contentDisposition.match(/filename="([^"]+)"/);
                    if (asciiMatch) {
                        filename = asciiMatch[1];
                    }
                }
            }

            // 确保文件名有扩展名
            if (!filename.endsWith('.xlsx')) {
                filename += '.xlsx';
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 显示成功Modal
            document.getElementById('exportSuccessMessage').textContent = `Excel文件 "${filename}" 已成功生成并开始下载`;
            const successModal = new bootstrap.Modal(document.getElementById('exportSuccessModal'));
            successModal.show();
        } else {
            const result = await response.json();
            // 显示错误Modal
            document.getElementById('exportErrorMessage').textContent = '导出失败：' + (result.message || '未知错误');
            const errorModal = new bootstrap.Modal(document.getElementById('exportErrorModal'));
            errorModal.show();
        }
    } catch (error) {
        console.error('导出数据错误:', error);

        // 显示错误Modal
        document.getElementById('exportErrorMessage').textContent = '导出失败：网络错误';
        const errorModal = new bootstrap.Modal(document.getElementById('exportErrorModal'));
        errorModal.show();
    }
}

function getExportConfig() {
    // 获取筛选条件
    const filters = {};

    if (document.getElementById('filterByStatus').checked) {
        const statusList = [];
        if (document.getElementById('statusPending').checked) statusList.push('待处理');
        if (document.getElementById('statusAssigned').checked) statusList.push('已分配');
        if (document.getElementById('statusInProgress').checked) statusList.push('处理中');
        if (document.getElementById('statusResolved').checked) statusList.push('已解决');
        if (document.getElementById('statusCompleted').checked) statusList.push('已完成');
        filters.status = statusList;
    }

    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    // 获取选择的字段
    const fields = [];
    const fieldMapping = {
        'fieldId': { key: 'id', label: '问题ID' },
        'fieldTitle': { key: 'title', label: '问题标题' },
        'fieldDescription': { key: 'description', label: '问题描述' },
        'fieldStatus': { key: 'status', label: '状态' },
        'fieldCreator': { key: 'creator', label: '创建人' },
        'fieldAssignee': { key: 'assignee', label: '分配给' },
        'fieldManager': { key: 'manager', label: '负责人' },
        'fieldTeam': { key: 'team', label: '产品线' },
        'fieldCreateTime': { key: 'create_time', label: '创建时间' },
        'fieldResolveTime': { key: 'resolve_time', label: '解决时间' },
        'fieldAttachments': { key: 'attachments', label: '附件数量' }
        // 优先级和分类暂未设计，先注释掉
        // 'fieldUpdateTime': { key: 'update_time', label: '更新时间' },
        // 'fieldPriority': { key: 'priority', label: '优先级' },
        // 'fieldCategory': { key: 'category', label: '分类' }
    };

    Object.keys(fieldMapping).forEach(fieldId => {
        if (document.getElementById(fieldId).checked) {
            fields.push(fieldMapping[fieldId]);
        }
    });

    // 获取导出格式
    const format = document.querySelector('input[name="exportFormat"]:checked').value;

    // 获取文件名
    const filename = document.getElementById('exportFileName').value || '问题列表报表';

    return {
        filters,
        fields,
        format,
        filename
    };
}

// 清理管理相关函数
async function loadCleanupStats() {
    console.log('加载清理统计信息...');

    try {
        const response = await fetch('/admin/notifications/cleanup/stats');
        const result = await response.json();

        if (result.success) {
            const stats = result.data;

            // 更新统计显示
            document.getElementById('totalNotifications').textContent = stats.total_notifications || 0;
            document.getElementById('userCount').textContent = stats.user_count || 0;
            document.getElementById('retentionDays').textContent = stats.retention_days || 30;
            document.getElementById('maxPerUser').textContent = stats.max_per_user || 100;
            document.getElementById('expiredCount').textContent = stats.expired_count || 0;
            document.getElementById('excessCount').textContent = stats.excess_count || 0;

            if (stats.oldest_notification) {
                const oldestDate = new Date(stats.oldest_notification);
                document.getElementById('oldestNotification').textContent =
                    `最旧通知: ${oldestDate.toLocaleDateString()} ${oldestDate.toLocaleTimeString()}`;
            } else {
                document.getElementById('oldestNotification').textContent = '最旧通知: 无通知记录';
            }

            console.log('清理统计信息加载成功');
        } else {
            console.warn('加载清理统计失败:', result.message);
        }

    } catch (error) {
        console.error('加载清理统计错误:', error);
    }
}

async function cleanupNotifications(type) {
    const typeNames = {
        'expired': '过期通知',
        'excess': '超量通知',
        'all': '所有通知'
    };

    const typeName = typeNames[type] || '通知';

    if (!confirm(`确定要清理${typeName}吗？此操作不可撤销。`)) {
        return;
    }

    // 找到对应的按钮并显示加载状态
    const buttons = document.querySelectorAll('.cleanup-action-card button');
    let targetButton = null;

    buttons.forEach(btn => {
        if ((type === 'expired' && btn.textContent.includes('清理过期')) ||
            (type === 'excess' && btn.textContent.includes('清理超量')) ||
            (type === 'all' && btn.textContent.includes('全面清理'))) {
            targetButton = btn;
        }
    });

    if (targetButton) {
        const originalText = targetButton.innerHTML;
        targetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清理中...';
        targetButton.disabled = true;
    }

    try {
        const response = await fetch('/admin/notifications/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ type: type })
        });

        const result = await response.json();

        if (result.success) {
            // 显示成功Modal
            document.getElementById('successModalTitle').textContent = '清理完成！';
            document.getElementById('successModalMessage').textContent = result.message;
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();

            // 重新加载统计信息
            await loadCleanupStats();

        } else {
            // 显示错误Modal
            document.getElementById('errorModalMessage').textContent = '清理失败：' + result.message;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        }

    } catch (error) {
        console.error('清理通知错误:', error);
        document.getElementById('errorModalMessage').textContent = '清理失败：网络错误';
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        errorModal.show();
    } finally {
        // 恢复按钮状态
        if (targetButton) {
            targetButton.innerHTML = targetButton.dataset.originalText ||
                (type === 'expired' ? '<i class="fas fa-trash-alt"></i> 清理过期' :
                 type === 'excess' ? '<i class="fas fa-compress-alt"></i> 清理超量' :
                 '<i class="fas fa-magic"></i> 全面清理');
            targetButton.disabled = false;
        }
    }
}

// 图表相关变量
let creatorChart = null;
let assigneeChart = null;
let chartData = null;

// 图表切换函数
function switchChart(type, chartType) {
    if (!chartData) return;

    // 更新按钮状态
    const buttons = document.querySelectorAll(`button[onclick*="${type}"]`);
    buttons.forEach(btn => btn.classList.remove('active'));

    // 找到当前点击的按钮并设置为活动状态
    const currentButton = document.querySelector(`button[onclick="switchChart('${type}', '${chartType}')"]`);
    if (currentButton) {
        currentButton.classList.add('active');
    }

    // 获取对应的图表实例和数据
    const chart = type === 'creator' ? creatorChart : assigneeChart;
    const data = type === 'creator' ? chartData.creator : chartData.assignee;

    if (chart) {
        chart.destroy();
    }

    // 创建新图表
    if (type === 'creator') {
        creatorChart = createChart('creatorChart', data, chartType, '已完成需求数量（提交人）');
    } else {
        assigneeChart = createChart('assigneeChart', data, chartType, '已完成需求数量（处理人）');
    }
}

// 创建图表函数
function createChart(canvasId, data, type, label) {
    const ctx = document.getElementById(canvasId).getContext('2d');

    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
    ];

    let config = {
        type: type,
        data: {
            labels: data.labels,
            datasets: [{
                label: label,
                data: data.values,
                backgroundColor: type === 'pie' ? colors.slice(0, data.labels.length) : 'rgba(54, 162, 235, 0.6)',
                borderColor: type === 'pie' ? colors.slice(0, data.labels.length) : 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: type === 'line' ? false : true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: type === 'pie',
                    position: 'right'
                },
                title: {
                    display: true,
                    text: label
                }
            },
            scales: type === 'pie' ? {} : {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        color: '#f0f0f0' // 设置网格线颜色为浅灰色
                    }
                },
                x: {
                    grid: {
                        color: '#f0f0f0' // 设置网格线颜色为浅灰色
                    }
                }
            },
            layout: {
                padding: 10 // 添加一些内边距
            }
        }
    };

    return new Chart(ctx, config);
}

// 加载图表数据
async function loadChartData() {
    try {
        const exportConfig = getExportConfig();

        const response = await fetch('/admin/reports/chart-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exportConfig)
        });

        const result = await response.json();

        if (result.success) {
            chartData = result.data;

            // 创建初始图表（折线图）
            creatorChart = createChart('creatorChart', chartData.creator, 'line', '已完成需求数量（提交人）');
            assigneeChart = createChart('assigneeChart', chartData.assignee, 'line', '已完成需求数量（处理人）');

            // 显示图表区域
            document.getElementById('chartsSection').style.display = 'block';
            document.getElementById('chartsSection').scrollIntoView({ behavior: 'smooth' });
        } else {
            console.error('加载图表数据失败:', result.message);
        }
    } catch (error) {
        console.error('加载图表数据错误:', error);
    }
}

// 导出图表为图片
function exportChart(type) {
    try {
        const chart = type === 'creator' ? creatorChart : assigneeChart;
        const chartTitle = type === 'creator' ? '提交人统计（已完成）' : '处理人统计（已完成）';

        if (!chart) {
            alert('图表尚未加载，请先预览数据');
            return;
        }

        // 显示背景选择弹窗
        showBackgroundChoiceModal(chart, chartTitle);

    } catch (error) {
        console.error('导出图表失败:', error);
        alert('导出图表失败，请重试');
    }
}

// 显示背景选择弹窗
function showBackgroundChoiceModal(chart, chartTitle) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="exportBackgroundModal" tabindex="-1" aria-labelledby="exportBackgroundModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exportBackgroundModalLabel">
                            <i class="fas fa-download me-2"></i>选择导出背景
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p class="mb-4">请选择图表导出的背景样式：</p>
                        <div class="row">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-primary w-100 h-100 py-4" onclick="downloadChart('${chart.canvas.id}', '${chartTitle}', true)">
                                    <i class="fas fa-square text-white bg-primary p-2 rounded mb-2"></i><br>
                                    <strong>白色背景</strong><br>
                                    <small class="text-muted">适合打印和文档</small>
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary w-100 h-100 py-4" onclick="downloadChart('${chart.canvas.id}', '${chartTitle}', false)">
                                    <i class="fas fa-square-full text-secondary mb-2" style="background: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 8px 8px; background-position: 0 0, 0 4px, 4px -4px, -4px 0px;"></i><br>
                                    <strong>透明背景</strong><br>
                                    <small class="text-muted">适合网页和设计</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('exportBackgroundModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('exportBackgroundModal'));
    modal.show();

    // 模态框关闭后移除DOM元素
    document.getElementById('exportBackgroundModal').addEventListener('hidden.bs.modal', function () {
        this.remove();
    });
}

// 下载图表
function downloadChart(canvasId, chartTitle, withWhiteBackground) {
    try {
        const originalCanvas = document.getElementById(canvasId);

        // 获取当前时间作为文件名的一部分
        const now = new Date();
        const timestamp = now.getFullYear() +
                         String(now.getMonth() + 1).padStart(2, '0') +
                         String(now.getDate()).padStart(2, '0') + '_' +
                         String(now.getHours()).padStart(2, '0') +
                         String(now.getMinutes()).padStart(2, '0') +
                         String(now.getSeconds()).padStart(2, '0');

        // 生成文件名
        const backgroundType = withWhiteBackground ? '白底' : '透明';
        const filename = `${chartTitle}_${backgroundType}_${timestamp}.png`;

        // 创建一个新的Canvas
        const newCanvas = document.createElement('canvas');
        const newCtx = newCanvas.getContext('2d');

        // 设置新Canvas的尺寸与原Canvas相同
        newCanvas.width = originalCanvas.width;
        newCanvas.height = originalCanvas.height;

        let url;

        if (withWhiteBackground) {
            // 填充白色背景
            newCtx.fillStyle = '#FFFFFF';
            newCtx.fillRect(0, 0, newCanvas.width, newCanvas.height);

            // 将原图表绘制到新Canvas上
            newCtx.drawImage(originalCanvas, 0, 0);

            // 将新Canvas转换为图片
            url = newCanvas.toDataURL('image/png');
        } else {
            // 对于透明背景，不填充任何背景色，直接绘制图表
            // 这样背景就是透明的
            newCtx.drawImage(originalCanvas, 0, 0);
            url = newCanvas.toDataURL('image/png');
        }

        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = url;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportBackgroundModal'));
        if (modal) {
            modal.hide();
        }

        // 显示成功提示
        showSuccessMessage(`图表已导出为 ${filename}`);

    } catch (error) {
        console.error('下载图表失败:', error);
        alert('下载图表失败，请重试');
    }
}

// 显示成功消息
function showSuccessMessage(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

</script>

<!-- Chart.js 库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

{% endblock %}
