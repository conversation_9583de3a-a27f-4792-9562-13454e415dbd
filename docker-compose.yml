# docker-compose.yml
# ReBugTracker - Bug跟踪系统 Docker Compose 配置
# 支持PostgreSQL和SQLite两种数据库模式

version: '3.8'

# 定义共享网络
networks:
  rebug_network:
    driver: bridge

services:
  # ReBugTracker 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rebugtracker_app
    ports:
      - "5000:5000"
    volumes:
      # 持久化上传文件和日志
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      # SQLite数据库文件持久化（如果使用SQLite模式）
      - app_data:/app/data
    environment:
      - FLASK_ENV=production
      - DB_TYPE=${DB_TYPE:-postgres}  # 默认使用PostgreSQL，可设置为sqlite
      # PostgreSQL配置（当DB_TYPE=postgres时使用）
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-ReBugTracker2024}
      - POSTGRES_DB=rebugtracker
      # SQLite配置（当DB_TYPE=sqlite时使用）
      - SQLITE_PATH=/app/data/rebugtracker.db
    depends_on:
      - db
    networks:
      - rebug_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # PostgreSQL 数据库服务
  db:
    image: postgres:15-alpine
    container_name: rebugtracker_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-ReBugTracker2024}
      - POSTGRES_DB=rebugtracker
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # 初始化脚本（如果需要）
      # - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - rebug_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d rebugtracker"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    # 可选：暴露端口用于外部连接
    # ports:
    #   - "5432:5432"

# 定义数据卷用于数据持久化
volumes:
  # PostgreSQL数据持久化
  postgres_data:
    driver: local
  # 应用上传文件持久化
  app_uploads:
    driver: local
  # 应用日志持久化
  app_logs:
    driver: local
  # SQLite数据库文件持久化（SQLite模式使用）
  app_data:
    driver: local
