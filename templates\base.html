<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求/Bug跟踪系统</title>
    <link rel="icon" href="{{ url_for('static', filename='RBT.ico') }}" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .notification-dropdown {
            min-width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }
        .notification-item {
            border-bottom: 1px solid #eee;
            padding: 10px;
            cursor: pointer;
        }
        .notification-item:hover {
            background-color: #f8f9fa;
        }
        .notification-item.unread {
            background-color: #e3f2fd;
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            min-width: 18px;
            text-align: center;
        }
        .notification-icon {
            position: relative;
        }
    </style>
</head>
<body>
    <!-- 导航栏已移除，使用页面内置的美化头部 -->

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    {% if user %}
    <script>
        // 通知相关功能
        let notificationUpdateInterval;

        // 页面加载时初始化通知
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            // 每30秒更新一次通知
            notificationUpdateInterval = setInterval(loadNotifications, 30000);
        });

        // 加载通知
        async function loadNotifications() {
            try {
                const response = await fetch('/api/notifications');
                const data = await response.json();

                if (data.success) {
                    updateNotificationBadge(data.unread_count);
                    updateNotificationList(data.notifications);
                }
            } catch (error) {
                console.error('加载通知失败:', error);
            }
        }

        // 更新通知徽章
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notificationBadge');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }

        // 更新通知列表
        function updateNotificationList(notifications) {
            const listContainer = document.getElementById('notificationList');

            if (notifications.length === 0) {
                listContainer.innerHTML = `
                    <li class="text-center p-4">
                        <i class="fas fa-bell-slash fa-2x text-muted mb-3"></i>
                        <div class="text-muted">暂无通知</div>
                    </li>
                `;
                return;
            }

            let html = '';
            notifications.forEach(notification => {
                const isUnread = !notification.read_status;
                const timeAgo = formatTimeAgo(notification.created_at);

                // 处理通知内容，保留完整内容但限制显示行数
                const content = notification.content || '';
                const title = notification.title || '无标题';

                html += `
                    <li class="notification-item ${isUnread ? 'unread' : ''}"
                        onclick="markAsRead(event, ${notification.id}, ${notification.related_bug_id || 'null'})"
                        title="${content}">
                        <div class="notification-title">${title}</div>
                        <div class="notification-content">${content}</div>
                        <div class="notification-meta">
                            <div class="notification-time">
                                <i class="fas fa-clock"></i>
                                <span>${timeAgo}</span>
                            </div>
                            <div class="notification-status ${isUnread ? 'unread' : 'read'}">
                                <i class="fas fa-${isUnread ? 'circle' : 'check-circle'}"></i>
                                <span>${isUnread ? '未读' : '已读'}</span>
                            </div>
                        </div>
                    </li>
                `;
            });

            listContainer.innerHTML = html;
        }

        // 标记通知为已读
        async function markAsRead(event, notificationId, bugId) {
            // 阻止事件冒泡，防止关闭下拉菜单
            if (event) {
                event.stopPropagation();
            }

            try {
                await fetch('/api/notifications/read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ notification_id: notificationId })
                });

                // 如果有关联的问题，跳转到问题详情
                if (bugId) {
                    window.location.href = `/bug/${bugId}`;
                } else {
                    // 重新加载通知
                    loadNotifications();
                }
            } catch (error) {
                console.error('标记已读失败:', error);
            }
        }

        // 标记所有通知为已读
        async function markAllAsRead(event) {
            // 阻止事件冒泡，防止关闭下拉菜单
            if (event) {
                event.stopPropagation();
            }

            try {
                await fetch('/api/notifications/read-all', {
                    method: 'POST'
                });
                loadNotifications();
            } catch (error) {
                console.error('标记全部已读失败:', error);
            }
        }

        // 格式化时间
        function formatTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;
            return date.toLocaleDateString();
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (notificationUpdateInterval) {
                clearInterval(notificationUpdateInterval);
            }
        });
    </script>
    {% endif %}
</body>
</html>
