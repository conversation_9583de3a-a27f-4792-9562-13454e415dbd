# ReBugTracker 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 数据库类型选择 (必须设置)
# 可选值: postgres, sqlite
DB_TYPE=postgres

# PostgreSQL 数据库配置（当 DB_TYPE=postgres 时使用）
# 注意：这些是config.py中的实际默认值
DATABASE_NAME=postgres
DATABASE_USER=postgres
DATABASE_PASSWORD=$RFV5tgb
DATABASE_HOST=localhost
DATABASE_PORT=5432

# 生产环境PostgreSQL推荐配置
# DATABASE_NAME=rebugtracker
# DATABASE_USER=rebugtracker_user
# DATABASE_PASSWORD=your_secure_password
# DATABASE_HOST=localhost
# DATABASE_PORT=5432

# SQLite 数据库配置（当 DB_TYPE=sqlite 时使用）
SQLITE_DB_PATH=rebugtracker.db

# Docker环境SQLite配置
# SQLITE_DB_PATH=/app/data/rebugtracker.db

# Flask 应用配置
FLASK_ENV=production
FLASK_SECRET_KEY=your-secret-key-here

# 应用端口配置
APP_PORT=5000

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/rebugtracker.log

# 时区配置
TZ=Asia/Shanghai

# Docker Compose 专用配置
POSTGRES_PASSWORD=ReBugTracker2024
POSTGRES_USER=postgres
POSTGRES_DB=rebugtracker
POSTGRES_HOST=db


