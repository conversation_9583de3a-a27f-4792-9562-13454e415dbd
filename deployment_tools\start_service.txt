@echo off
REM ReBugTracker Windows服务快速启动脚本

setlocal enabledelayedexpansion

REM 固定工作目录，解决管理员权限下路径问题
cd /d "%~dp0.."

set "SERVICE_NAME=ReBugTracker"
set "PROJECT_DIR=%cd%"

echo ReBugTracker Windows服务快速启动
echo ========================================
echo.
echo 项目目录: %PROJECT_DIR%
echo.

REM 检查服务是否存在
sc query "%SERVICE_NAME%" >nul 2>&1
if errorlevel 1 (
    echo [错误] 服务 %SERVICE_NAME% 未安装
    echo.
    echo 请先安装Windows服务:
    echo   deployment_tools\install_windows_service.bat
    echo.
    pause
    exit /b 1
)

REM 检查服务状态
for /f "tokens=4" %%i in ('sc query "%SERVICE_NAME%" ^| findstr "STATE"') do set SERVICE_STATE=%%i

if "%SERVICE_STATE%"=="RUNNING" (
    echo [成功] 服务已在运行中
    echo 访问地址: http://localhost:8000
) else (
    echo [信息] 启动服务...
    net start "%SERVICE_NAME%"
    if errorlevel 1 (
        echo [错误] 服务启动失败
        echo 请检查日志或使用管理工具: deployment_tools\manage_windows_service.bat
    ) else (
        echo [成功] 服务启动成功
        echo 访问地址: http://localhost:8000
    )
)

echo.
echo 服务管理:
echo   管理工具: deployment_tools\manage_windows_service.bat
echo   停止服务: net stop %SERVICE_NAME%
echo   查看状态: sc query %SERVICE_NAME%
echo.

set /p open_web="是否打开Web界面? (y/n): "
if /i "%open_web%"=="y" (
    start http://localhost:8000
)

pause
