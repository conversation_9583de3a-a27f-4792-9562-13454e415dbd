<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>填写处理详情 - ReBugTracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
        }

        .resolve-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: calc(100vh - 40px);
            padding: 20px 0;
        }

        .resolve-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .resolve-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .header-top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-left {
            flex: 1;
        }

        .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 12px;
        }

        .user-info-row {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #fff;
            font-size: 14px;
        }

        .user-info-row .user-name {
            font-weight: 600;
            font-size: 16px;
        }

        .user-info-row .user-role {
            display: flex;
            align-items: center;
            gap: 5px;
            opacity: 0.9;
        }

        .user-info-row .user-team {
            display: flex;
            align-items: center;
            gap: 5px;
            opacity: 0.8;
            font-size: 13px;
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .btn-modern {
            padding: 12px 25px;
            border-radius: 25px;
            border: none;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            cursor: pointer;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
            color: white;
        }

        .btn-danger-modern {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .btn-success-modern {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(86, 171, 47, 0.4);
            color: white;
        }

        .btn-secondary-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .main-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .bug-info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .bug-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bug-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .bug-meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            font-size: 14px;
        }

        .bug-meta-item i {
            color: #667eea;
            width: 16px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 15px;
        }

        .form-control-modern {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 14px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: #f8f9fa;
            min-height: 200px;
            resize: vertical;
        }

        .form-control-modern:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-start;
            flex-wrap: wrap;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #f8f9fa;
        }

        .container-fluid {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        @media (max-width: 768px) {
            .header-top-row {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .header-right {
                align-items: center;
            }

            .user-info-row {
                justify-content: center;
            }

            .header-title {
                font-size: 2rem;
            }

            .bug-meta {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="resolve-container">
        <div class="container-fluid">
            <!-- 页面头部 -->
            <div class="resolve-header">
                <div class="header-content">
                    <div class="header-top-row">
                        <div class="header-left">
                            <div class="header-title">
                                <i class="fas fa-tools me-3"></i>填写处理详情
                            </div>
                            <div class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>详细记录问题处理过程和解决方案
                            </div>
                        </div>
                        <div class="header-right">
                            <div class="user-info-row">
                                <span class="user-name">
                                    <i class="fas fa-user me-2"></i>{{ user.chinese_name or user.username }}
                                </span>
                                <span class="user-role">
                                    <i class="fas fa-id-badge me-1"></i>{{ user.role }}
                                </span>
                                {% if user.team %}
                                <span class="user-team">
                                    <i class="fas fa-users me-1"></i>{{ user.team }}
                                </span>
                                {% endif %}
                            </div>
                            <div class="header-actions">
                                <a href="/" class="btn-modern btn-secondary-modern">
                                    <i class="fas fa-home"></i>
                                    返回首页
                                </a>
                                <a href="/logout" class="btn-modern btn-danger-modern">
                                    <i class="fas fa-sign-out-alt"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="main-content">
                <!-- 问题信息卡片 -->
                <div class="bug-info-card">
                    <div class="bug-title">
                        <i class="fas fa-bug"></i>
                        {{ bug.title }}
                    </div>
                    <div class="bug-meta">
                        <div class="bug-meta-item">
                            <i class="fas fa-user-plus"></i>
                            <strong>提交人:</strong> {{ bug.creator_name }}
                        </div>
                        <div class="bug-meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <strong>创建时间:</strong> {{ bug.created_at }}
                        </div>
                        <div class="bug-meta-item">
                            <i class="fas fa-flag"></i>
                            <strong>当前状态:</strong>
                            <span class="badge bg-warning text-dark">{{ bug.status }}</span>
                        </div>
                        {% if bug.assignee_name %}
                        <div class="bug-meta-item">
                            <i class="fas fa-user-check"></i>
                            <strong>负责人:</strong> {{ bug.assignee_name }}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 处理详情表单 -->
                <div class="form-section">
                    <h3>
                        <i class="fas fa-edit"></i>
                        处理详情
                    </h3>
                    <form action="/bug/resolve/{{ bug.id }}" method="POST" id="resolveForm">
                        <div class="form-group">
                            <label for="resolution" class="form-label">
                                <i class="fas fa-clipboard-list me-2"></i>详细描述处理过程和结果
                            </label>
                            <textarea
                                id="resolution"
                                name="resolution"
                                class="form-control-modern"
                                placeholder="请详细描述：&#10;1. 问题分析过程&#10;2. 采取的解决措施&#10;3. 最终解决结果&#10;4. 预防措施建议&#10;&#10;示例：&#10;经过分析发现是由于...导致的问题。采取了以下措施：...最终成功解决了问题，系统恢复正常运行。建议后续..."
                                required></textarea>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="btn-modern btn-success-modern">
                                <i class="fas fa-check-circle"></i>
                                提交处理详情
                            </button>
                            <a href="/" class="btn-modern btn-secondary-modern">
                                <i class="fas fa-times"></i>
                                取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 表单提交处理
        function handleSubmit(event) {
            event.preventDefault();

            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // 显示加载状态
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            submitBtn.disabled = true;

            fetch(event.target.action, {
                method: 'POST',
                body: new FormData(event.target)
            })
            .then(response => {
                if(response.ok) {
                    // 显示成功状态
                    submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>提交成功';
                    submitBtn.className = 'btn-modern btn-success-modern';

                    // 显示成功消息
                    showSuccessMessage('处理详情已提交成功！即将返回首页...');

                    // 2秒后跳转
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    throw new Error('提交失败');
                }
            })
            .catch(error => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                showErrorMessage('提交失败，请重试');
            });
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // 5秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // 绑定表单提交事件
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('resolveForm');
            if (form) {
                form.addEventListener('submit', handleSubmit);
            }
        });
    </script>
</body>
</html>
