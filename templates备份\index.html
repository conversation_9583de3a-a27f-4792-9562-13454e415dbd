{% extends "base.html" %}

{% block content %}
<style>
    .dashboard-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 80px);
        padding: 20px 0;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .header-top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-left {
        flex: 1;
    }

    .header-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
    }

    .user-info-row {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #fff;
        font-size: 14px;
    }

    .user-info-row .user-name {
        font-weight: 600;
        font-size: 16px;
    }

    .user-info-row .user-role {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.9;
    }

    .user-info-row .user-team {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.8;
        font-size: 13px;
    }

    .header-bottom-row {
        width: 100%;
    }
    
    .header-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }
    
    /* 原有的user-info样式已被user-info-row替代 */
    
    .header-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .header-actions .dropdown {
        position: relative;
    }

    /* 确保通知下拉菜单不被其他元素遮挡 */
    .notification-dropdown.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 动态调整通知位置的类 */
    .notification-dropdown.position-bottom {
        top: 100% !important;
        transform: translateX(-10px) translateY(10px) !important;
    }

    .notification-dropdown.position-top {
        top: 0 !important;
        transform: translateX(-10px) translateY(-100%) !important;
    }

    /* 问题操作按钮样式 */
    .bug-actions {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
    }

    .btn-view {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a4190);
        color: white;
        transform: translateY(-1px);
    }

    .btn-assign {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .btn-assign:hover {
        background: linear-gradient(135deg, #e081e9, #e3455a);
        color: white;
        transform: translateY(-1px);
    }

    .btn-confirm {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
    }

    .btn-confirm:hover {
        background: linear-gradient(135deg, #3d9aec, #00e0ec);
        color: white;
        transform: translateY(-1px);
    }

    .btn-resolve {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        color: white;
    }

    .btn-resolve:hover {
        background: linear-gradient(135deg, #31d769, #26e7c5);
        color: white;
        transform: translateY(-1px);
    }

    .btn-complete {
        background: linear-gradient(135deg, #fa709a, #fee140);
        color: white;
    }

    .btn-complete:hover {
        background: linear-gradient(135deg, #e85e88, #ecd02e);
        color: white;
        transform: translateY(-1px);
    }

    .btn-delete {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #e55555, #dc4840);
        color: white;
        transform: translateY(-1px);
    }
    
    .btn-modern {
        padding: 10px 20px;
        border-radius: 25px;
        border: none;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        color: white;
    }
    
    .btn-danger-modern {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }
    
    .btn-danger-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
        color: white;
    }

    /* 通知按钮青绿色样式 - 高优先级 */
    .btn-modern.btn-info-modern,
    button.btn-modern.btn-info-modern,
    #notificationDropdown.btn-modern.btn-info-modern {
        background: linear-gradient(135deg, #38f9d7 0%, #4dd0e1 100%) !important;
        color: white !important;
        position: relative;
        border: none !important;
    }

    .btn-modern.btn-info-modern:hover,
    button.btn-modern.btn-info-modern:hover,
    #notificationDropdown.btn-modern.btn-info-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 20px rgba(56, 249, 215, 0.4) !important;
        color: white !important;
        background: linear-gradient(135deg, #26e6cb 0%, #38f9d7 100%) !important;
    }

    .notification-dropdown {
        min-width: 380px;
        max-width: 450px;
        max-height: 80vh;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        padding: 0;
        position: fixed !important;
        z-index: 10000 !important;
        margin: 0 !important;
        /* 初始位置，会被JavaScript动态调整 */
        right: auto !important;
        left: auto !important;
        top: auto !important;
        bottom: auto !important;
        background: white;
        /* 默认隐藏，但不使用!important，让JavaScript可以覆盖 */
        display: none;
        overflow: visible;
    }

    .notification-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding: 15px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .notification-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(3px);
    }

    .notification-item.unread {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-left: 4px solid #2196f3;
    }

    .notification-item.unread:hover {
        background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
    }

    .notification-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 1.4;
    }

    .notification-content {
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 8px;
        word-wrap: break-word;
        max-height: 60px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .notification-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #999;
    }

    .notification-time {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .notification-status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
    }

    .notification-status.unread {
        color: #2196f3;
    }

    .notification-status.read {
        color: #4caf50;
    }

    .notification-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
        color: white;
        border-radius: 50%;
        padding: 4px 8px;
        font-size: 11px;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
        border: 2px solid white;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .stats-container {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 12px;
        margin: 15px;
        border: 1px solid #e9ecef;
    }

    .stats-grid-inline {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .stat-card-inline {
        background: white;
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: 8px;
        border: 1px solid transparent;
    }

    .stat-card-inline.clickable {
        cursor: pointer;
    }

    .stat-card-inline.clickable:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
        border-color: #667eea;
    }

    .stat-card-inline.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .stat-card-inline.active .stat-number-inline,
    .stat-card-inline.active .stat-label-inline {
        color: white;
    }

    .stat-icon-inline {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        flex-shrink: 0;
    }

    .stat-content-inline {
        flex: 1;
    }

    .stat-number-inline {
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1px;
        line-height: 1;
    }

    .stat-label-inline {
        color: #666;
        font-size: 0.75rem;
        font-weight: 500;
        line-height: 1;
    }
    
    .filter-section {
        background: white;
        padding: 25px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .filter-section-inline {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 12px 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .filter-section-inline .filter-title {
        color: #333;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        white-space: nowrap;
    }

    .filter-section-inline .filter-options {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        flex: 1;
    }

    .filter-section-inline .filter-checkbox {
        background: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 6px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .filter-section-inline .filter-checkbox:hover {
        background: rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }
    
    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .filter-options {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        align-items: center;
    }
    
    .filter-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 15px;
        background: #f8f9fa;
        border-radius: 25px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .filter-checkbox:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }
    
    .filter-checkbox input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
    }
    
    .bug-status {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .status-待处理 { background: #fff3cd; color: #856404; }
    .status-已分配 { background: #d1ecf1; color: #0c5460; }
    .status-处理中 { background: #d4edda; color: #155724; }
    .status-已解决 { background: #f8d7da; color: #721c24; }
    .status-已完成 { background: #e2e3e5; color: #383d41; }
    
    .filter-section-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
        padding: 20px 0;
    }

    .bugs-section {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .bugs-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .bugs-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .bugs-title i {
        color: #667eea;
        font-size: 1.1rem;
    }
    
    .bugs-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 500;
    }
    
    .bug-item {
        padding: 20px 25px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    /* 斑马纹效果 - 奇数行（明显的蓝色调） */
    .bug-item:nth-child(odd) {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-left: 6px solid #2196f3;
    }

    /* 斑马纹效果 - 偶数行（明显的绿色调） */
    .bug-item:nth-child(even) {
        background: linear-gradient(135deg, #f1f8e9, #dcedc8);
        border-left: 6px solid #4caf50;
    }

    .bug-item:hover {
        transform: translateX(10px);
    }

    /* 悬停时的特殊效果 */
    .bug-item:nth-child(odd):hover {
        background: linear-gradient(135deg, #bbdefb, #90caf9);
        border-left-color: #1976d2;
        box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
    }

    .bug-item:nth-child(even):hover {
        background: linear-gradient(135deg, #dcedc8, #c8e6c9);
        border-left-color: #388e3c;
        box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
    }

    .bug-item:last-child {
        border-bottom: none;
    }
    
    .bug-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        gap: 15px;
    }
    
    .bug-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
        flex: 1;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }
    
    .bug-id {
        background: #667eea;
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .bug-meta {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    
    .bug-meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #666;
        font-size: 0.9rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-icon {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    /* 通知列表滚动条样式 */
    #notificationList::-webkit-scrollbar {
        width: 6px;
    }

    #notificationList::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #notificationList::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    #notificationList::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 现代化Modal样式 */
    .modern-modal {
        border: none;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    /* 确认Modal样式 */
    .confirm-header {
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .confirm-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .confirm-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .confirm-animation {
        margin-bottom: 25px;
    }

    .confirm-icon-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        animation: pulse 2s infinite;
    }

    .confirm-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .confirm-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        display: flex;
        gap: 15px;
        justify-content: center;
    }

    .confirm-footer .btn-modern {
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .confirm-footer .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    }

    .confirm-footer .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .confirm-footer .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .confirm-footer .btn-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    /* 成功Modal样式 */
    .success-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .success-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .success-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .success-animation {
        margin-bottom: 25px;
    }

    .checkmark {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #28a745;
        stroke-miterlimit: 10;
        margin: 0 auto 20px;
        box-shadow: inset 0px 0px 0px #28a745;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 2;
        stroke-miterlimit: 10;
        stroke: #28a745;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    .success-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .success-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        text-align: center;
    }

    .success-footer .btn-modern {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .success-footer .btn-modern:hover {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    /* 错误Modal样式 */
    .error-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .error-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .error-body {
        padding: 40px 30px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .error-animation {
        margin-bottom: 25px;
    }

    .error-icon-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        animation: shake 0.5s ease-in-out;
    }

    .error-message {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .error-footer {
        background: white;
        border: none;
        padding: 20px 30px;
        text-align: center;
    }

    .error-footer .btn-modern {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white !important;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 25px;
        border: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .error-footer .btn-modern:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    /* 动画效果 */
    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {
        0%, 100% {
            transform: none;
        }
        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 30px #28a745;
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes shake {
        0%, 100% {
            transform: translateX(0);
        }
        10%, 30%, 50%, 70%, 90% {
            transform: translateX(-5px);
        }
        20%, 40%, 60%, 80% {
            transform: translateX(5px);
        }
    }

    @media (max-width: 768px) {
        .header-content {
            gap: 10px;
        }

        .header-top-row {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .header-right {
            align-items: center;
            gap: 15px;
        }

        .user-info-row {
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;
        }

        .filter-section-inline {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .filter-section-inline .filter-options {
            justify-content: center;
            width: 100%;
        }

        .notification-dropdown {
            position: fixed !important;
            left: 5vw !important;
            right: 5vw !important;
            top: auto !important;
            bottom: auto !important;
            width: 90vw !important;
            min-width: auto !important;
            max-width: none !important;
            transform: none !important;
        }
        
        .header-title {
            font-size: 2rem;
        }
        
        .stats-grid-inline {
            grid-template-columns: repeat(2, 1fr);
        }

        .stat-card-inline {
            flex-direction: column;
            text-align: center;
            gap: 6px;
            padding: 8px;
        }

        .stat-icon-inline {
            width: 28px;
            height: 28px;
            font-size: 0.8rem;
        }

        .stat-number-inline {
            font-size: 1.2rem;
        }

        .stat-label-inline {
            font-size: 0.7rem;
        }

        .stats-container {
            margin: 10px;
            padding: 10px;
        }
    }

    @media (max-width: 480px) {
        .stats-grid-inline {
            grid-template-columns: 1fr;
        }
        
        .filter-options {
            justify-content: center;
        }
        
        .bug-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .bug-meta {
            justify-content: flex-start;
        }
    }
</style>

<div class="dashboard-container">
    <div class="container">
        <!-- 头部区域 -->
        <div class="dashboard-header">
            <div class="header-content">
                <!-- 第一行：标题和用户信息 -->
                <div class="header-top-row">
                    <div class="header-left">
                        <h1 class="header-title">
                            <i class="fas fa-bug"></i>
                            问题管理中心
                        </h1>
                        <p class="header-subtitle">高效跟踪和管理项目问题</p>
                    </div>

                    <div class="header-right">
                        <div class="user-info-row">
                            <span class="user-name">{{ user.chinese_name or user.username }}</span>
                            <span class="user-role">
                                {% if user.role_en == 'ssz' %}
                                    <i class="fas fa-tools"></i> 实施组人员
                                {% elif user.role_en == 'fzr' %}
                                    <i class="fas fa-user-tie"></i> 负责人
                                {% elif user.role_en == 'zncy' %}
                                    <i class="fas fa-user"></i> 组内成员
                                {% elif user.role_en == 'gly' %}
                                    <i class="fas fa-user-shield"></i> 管理员
                                {% else %}
                                    <i class="fas fa-user"></i> {{ user.role }}
                                {% endif %}
                            </span>
                            {% if user.team %}
                            <span class="user-team">
                                <i class="fas fa-building"></i> {{ user.team }}
                            </span>
                            {% endif %}
                        </div>

                        <!-- 通知设置链接 -->
                        <a href="/user/settings" class="btn btn-outline-light btn-sm" title="通知设置" style="margin-right: 10px;">
                            <i class="fas fa-bell-slash"></i>
                            通知设置
                        </a>

                        <div class="header-actions">
                            <!-- 通知按钮 -->
                            <div class="dropdown">
                                <button class="btn-modern btn-info-modern dropdown-toggle" type="button" id="notificationDropdown" aria-expanded="false" onclick="toggleNotificationDropdown(event); return false;" style="background: linear-gradient(135deg, #81c784 0%, #a5d6a7 100%) !important; border: none !important;">
                                    <i class="fas fa-bell"></i>
                                    通知
                                    <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-start notification-dropdown" aria-labelledby="notificationDropdown">
                                    <li class="dropdown-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 20px; margin: 0; border-radius: 15px 15px 0 0;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="fas fa-bell me-2"></i>
                                                <strong>通知中心</strong>
                                            </div>
                                            <button class="btn btn-sm btn-light" onclick="markAllAsRead(event)" style="border-radius: 20px; font-size: 11px;">
                                                <i class="fas fa-check-double me-1"></i>全部已读
                                            </button>
                                        </div>
                                    </li>
                                    <li style="padding: 0; margin: 0; background: white;">
                                        <div id="notificationList" style="max-height: 400px; overflow-y: auto; background: white;">
                                            <div class="text-center p-4">
                                                <i class="fas fa-spinner fa-spin text-primary"></i>
                                                <div class="mt-2 text-muted">加载中...</div>
                                            </div>
                                        </div>
                                    </li>
                                    <li style="border-top: 1px solid rgba(0,0,0,0.08); margin: 0; background: white;">
                                        <a class="dropdown-item text-center py-3" href="/notifications" style="color: #667eea; font-weight: 500;">
                                            <i class="fas fa-external-link-alt me-2"></i>查看全部通知
                                        </a>
                                    </li>
                                </ul>
                            </div>

                    {% if user.role_en == 'ssz' %}
                            <a href="/submit" class="btn-modern btn-primary-modern">
                                <i class="fas fa-plus"></i>
                                提交新问题
                            </a>
                    {% endif %}

                            <a href="/logout" class="btn-modern btn-danger-modern">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                </div>
            </div>
        </div>

    {% if request.args.get('message') %}
    <div class="alert alert-success" style="padding:10px; background:#dff0d8; color:#3c763d; border-radius:4px; margin-bottom:15px;">
        {{ request.args.get('message') }}
    </div>
    {% endif %}
            </div>
        </div>
        <!-- 状态筛选 -->
<div class="filter-section-container">
    <div class="container">
        <div class="header-bottom-row">
            <div class="filter-section-inline">
                <div class="filter-title">
                    <i class="fas fa-filter"></i>
                    状态筛选
                </div>
                <div class="filter-options">
                    <label class="filter-checkbox">
                        <input type="checkbox" class="status-checkbox" value="待处理" checked>
                        <span class="bug-status status-待处理">待处理</span>
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" class="status-checkbox" value="已分配" checked>
                        <span class="bug-status status-已分配">已分配</span>
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" class="status-checkbox" value="处理中" checked>
                        <span class="bug-status status-处理中">处理中</span>
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" class="status-checkbox" value="已解决" checked>
                        <span class="bug-status status-已解决">已解决</span>
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" class="status-checkbox" value="已完成" checked>
                        <span class="bug-status status-已完成">已完成</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 问题列表 -->
<div class="bugs-section">
    <div class="container">
        <div class="bugs-header">
            <h2 class="bugs-title">
                <i class="fas fa-list-ul"></i>
                问题管理中心
            </h2>
            <div class="bugs-count">
                <span id="visibleBugsCount">{{ bugs|length }}</span> 个问题
            </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-container">
            <div class="stats-grid-inline">
                <div class="stat-card-inline clickable" data-status="all" onclick="filterByStatus('all')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="totalBugs">{{ bugs|length }}</div>
                        <div class="stat-label-inline">总问题数</div>
                    </div>
                </div>

                {% if user.role_en == 'fzr' %}
                <div class="stat-card-inline clickable active" data-status="not-completed" onclick="filterByStatus('not-completed')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="notCompletedBugs">0</div>
                        <div class="stat-label-inline">未完成问题</div>
                    </div>
                </div>
                {% endif %}

                <div class="stat-card-inline clickable" data-status="pending" onclick="filterByStatus('pending')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="pendingBugs">0</div>
                        <div class="stat-label-inline">待处理</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="assigned" onclick="filterByStatus('assigned')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="assignedBugs">0</div>
                        <div class="stat-label-inline">已分配</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="processing" onclick="filterByStatus('processing')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="processingBugs">0</div>
                        <div class="stat-label-inline">处理中</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="resolved" onclick="filterByStatus('resolved')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="resolvedBugs">0</div>
                        <div class="stat-label-inline">已解决</div>
                    </div>
                </div>

                <div class="stat-card-inline clickable" data-status="completed" onclick="filterByStatus('completed')">
                    <div class="stat-icon-inline" style="background: linear-gradient(135deg, #6c757d, #495057);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content-inline">
                        <div class="stat-number-inline" id="completedBugs">0</div>
                        <div class="stat-label-inline">已完成</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="bugsList">
            {% if bugs %}
                {% for bug in bugs %}
                <div class="bug-item" data-status="{{ bug.status }}" onclick="window.location.href='/bug/{{ bug.id }}'">
                    <div class="bug-header">
                        <h3 class="bug-title">{{ bug.title }}</h3>
                        <div class="bug-id">#{{ bug.id }}</div>
                    </div>

                    <div class="bug-meta">
                        <div class="bug-meta-item">
                            <i class="fas fa-tag"></i>
                            <span class="bug-status status-{{ bug.status }}">{{ bug.status }}</span>
                        </div>

                        <div class="bug-meta-item">
                            <i class="fas fa-user"></i>
                            <span>{{ bug.creator_name or '未知' }}</span>
                        </div>

                        {% if bug.assignee_name %}
                        <div class="bug-meta-item">
                            <i class="fas fa-user-check"></i>
                            <span>{{ bug.assignee_name }}</span>
                        </div>
                        {% endif %}

                        <div class="bug-meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ bug.created_at }}</span>
                        </div>

                        {% if bug.project %}
                        <div class="bug-meta-item">
                            <i class="fas fa-folder"></i>
                            <span>{{ bug.project }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="bug-actions">
                        <a href="/bug/{{ bug.id }}" class="btn-action btn-view">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </a>

                        {% if user.role_en == 'fzr' and bug.status != '已完成' %}
                        <a href="/bug/assign/{{ bug.id }}" class="btn-action btn-assign">
                            <i class="fas fa-user-plus"></i>
                            指派
                        </a>
                        {% endif %}

                        {% if user.role_en == 'zncy' and bug.assigned_to == user.id and bug.status == '已分配' %}
                        <button onclick="event.stopPropagation(); confirmReceive('{{ bug.id }}')" class="btn-action btn-confirm">
                            <i class="fas fa-check"></i>
                            确认接收
                        </button>
                        {% endif %}

                        {% if user.role_en == 'zncy' and bug.assigned_to == user.id and bug.status == '处理中' %}
                        <a href="/bug/resolve/'{{ bug.id }}'" class="btn-action btn-resolve">
                            <i class="fas fa-tools"></i>
                            解决问题
                        </a>
                        {% endif %}

                        {% if user.role_en == 'ssz' and bug.status == '已解决' and bug.created_by == user.id %}
                        <button onclick="event.stopPropagation(); confirmComplete('{{ bug.id }}')" class="btn-action btn-complete">
                            <i class="fas fa-check-circle"></i>
                            确认闭环
                        </button>
                        {% endif %}

                        {% if user.role_en == 'gly' or (user.role_en == 'ssz' and bug.created_by == user.id) %}
                        <button onclick="event.stopPropagation(); deleteBug('{{ bug.id }}')" class="btn-action btn-delete">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h3>暂无问题</h3>
                    <p>当前没有任何问题记录</p>
                    {% if user.role_en == 'ssz' %}
                    <a href="/submit" class="btn-modern btn-primary-modern" style="margin-top: 20px;">
                        <i class="fas fa-plus"></i>
                        提交第一个问题
                    </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

    </div>
    </div>

<!-- 现代化确认Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header confirm-header">
                <div class="confirm-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
            </div>
            <div class="modal-body confirm-body">
                <div class="confirm-animation">
                    <div class="confirm-icon-large">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <p class="confirm-message" id="confirmModalMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer confirm-footer">
                <button type="button" class="btn-modern btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn-modern btn-primary" id="confirmModalBtn">
                    <i class="fas fa-check me-2"></i>确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 现代化成功提示Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header success-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h5 class="modal-title" id="successModalTitle">操作成功！</h5>
            </div>
            <div class="modal-body success-body">
                <div class="success-animation">
                    <div class="checkmark">
                        <svg class="checkmark__svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                        </svg>
                    </div>
                </div>
                <p class="success-message" id="successModalMessage">操作已成功完成</p>
            </div>
            <div class="modal-footer success-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 现代化错误提示Modal -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header error-header">
                <div class="error-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h5 class="modal-title">操作失败</h5>
            </div>
            <div class="modal-body error-body">
                <div class="error-animation">
                    <div class="error-icon-large">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <p class="error-message" id="errorModalMessage">操作失败，请稍后重试</p>
            </div>
            <div class="modal-footer error-footer">
                <button type="button" class="btn-modern btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 统计数据计算
function updateStats() {
    const allBugs = document.querySelectorAll('.bug-item');
    const totalCount = allBugs.length;

    let pendingCount = 0;
    let assignedCount = 0;
    let processingCount = 0;
    let resolvedCount = 0;
    let completedCount = 0;

    allBugs.forEach(bug => {
        const status = bug.dataset.status;
        if (status === '待处理') {
            pendingCount++;
        } else if (status === '已分配') {
            assignedCount++;
        } else if (status === '处理中') {
            processingCount++;
        } else if (status === '已解决') {
            resolvedCount++;
        } else if (status === '已完成') {
            completedCount++;
        }
    });

    document.getElementById('totalBugs').textContent = totalCount;
    document.getElementById('pendingBugs').textContent = pendingCount;
    document.getElementById('assignedBugs').textContent = assignedCount;
    document.getElementById('processingBugs').textContent = processingCount;
    document.getElementById('resolvedBugs').textContent = resolvedCount;
    document.getElementById('completedBugs').textContent = completedCount;

    // 计算未完成问题数（总数减去已完成）
    const notCompletedCount = totalCount - completedCount;
    const notCompletedElement = document.getElementById('notCompletedBugs');
    if (notCompletedElement) {
        notCompletedElement.textContent = notCompletedCount;
    }
}

// 当前筛选状态
let currentFilter = 'all';

// 按状态筛选问题
function filterByStatus(status) {
    currentFilter = status;

    // 更新卡片激活状态
    document.querySelectorAll('.stat-card-inline').forEach(card => {
        card.classList.remove('active');
    });
    document.querySelector(`[data-status="${status}"]`).classList.add('active');

    // 筛选问题
    const bugItems = document.querySelectorAll('.bug-item');
    let visibleCount = 0;

    bugItems.forEach(bug => {
        const bugStatus = bug.dataset.status;
        let shouldShow = false;

        if (status === 'all') {
            shouldShow = true;
        } else if (status === 'not-completed') {
            // 显示除了已完成之外的所有问题
            shouldShow = bugStatus !== '已完成';
        } else if (status === 'pending') {
            shouldShow = bugStatus === '待处理';
        } else if (status === 'assigned') {
            shouldShow = bugStatus === '已分配';
        } else if (status === 'processing') {
            shouldShow = bugStatus === '处理中';
        } else if (status === 'resolved') {
            shouldShow = bugStatus === '已解决';
        } else if (status === 'completed') {
            shouldShow = bugStatus === '已完成';
        }

        if (shouldShow) {
            bug.style.display = 'block';
            visibleCount++;
        } else {
            bug.style.display = 'none';
        }
    });

    // 更新显示的问题数量
    document.getElementById('visibleBugsCount').textContent = visibleCount;

    // 更新筛选区域的复选框状态
    updateFilterCheckboxes(status);
}

// 更新筛选复选框状态
function updateFilterCheckboxes(status) {
    const checkboxes = document.querySelectorAll('.status-checkbox');
    checkboxes.forEach(checkbox => {
        if (status === 'all') {
            checkbox.checked = true;
        } else if (status === 'not-completed') {
            // 不显示已完成的问题时，除了已完成都勾选
            const checkboxStatus = checkbox.value;
            checkbox.checked = checkboxStatus !== '已完成';
        } else {
            const checkboxStatus = checkbox.value;
            if (status === 'pending') {
                checkbox.checked = checkboxStatus === '待处理';
            } else if (status === 'assigned') {
                checkbox.checked = checkboxStatus === '已分配';
            } else if (status === 'processing') {
                checkbox.checked = checkboxStatus === '处理中';
            } else if (status === 'resolved') {
                checkbox.checked = checkboxStatus === '已解决';
            } else if (status === 'completed') {
                checkbox.checked = checkboxStatus === '已完成';
            } else {
                checkbox.checked = false;
            }
        }
    });
}

// 筛选功能（通过复选框）
function updateBugsList() {
    const checkboxes = document.querySelectorAll('.status-checkbox');
    const selectedStatuses = Array.from(checkboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

    const bugItems = document.querySelectorAll('.bug-item');
    let visibleCount = 0;

    bugItems.forEach(bug => {
        const status = bug.dataset.status;
        if (selectedStatuses.includes(status)) {
            bug.style.display = 'block';
            visibleCount++;
        } else {
            bug.style.display = 'none';
        }
    });

    document.getElementById('visibleBugsCount').textContent = visibleCount;

    // 重置统计卡片的激活状态
    document.querySelectorAll('.stat-card-inline').forEach(card => {
        card.classList.remove('active');
    });

    // 如果所有状态都选中，激活"总问题数"卡片
    if (selectedStatuses.length === checkboxes.length) {
        document.querySelector('[data-status="all"]').classList.add('active');
        currentFilter = 'all';
    } else {
        currentFilter = 'custom';
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStats();

    // 检查用户角色，如果是负责人，默认不显示已完成的问题
    const userRole = '{{ user.role_en }}';
    if (userRole === 'fzr') {
        // 负责人默认不显示已完成的问题
        filterByStatus('not-completed');
        // 默认激活"未完成问题"卡片（已在HTML中设置active类）
    } else {
        // 其他角色显示所有问题
        updateBugsList();
        // 默认激活"总问题数"卡片
        document.querySelector('[data-status="all"]').classList.add('active');
    }

    // 绑定筛选事件
    document.querySelectorAll('.status-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBugsList);
    });

    // 统计卡片动画
    const statCards = document.querySelectorAll('.stat-card-inline');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.animation = 'fadeInUp 0.6s ease-out forwards';
    });

    // 问题项动画
    const bugItems = document.querySelectorAll('.bug-item');
    bugItems.forEach((item, index) => {
        item.style.animationDelay = `${(index + statCards.length) * 0.05}s`;
        item.style.animation = 'fadeInUp 0.4s ease-out forwards';
    });
});

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// 通知中心位置调整
function adjustNotificationPosition() {
    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) return;

    console.log('Adjusting notification position...');

    // 获取按钮位置和视窗信息
    const buttonRect = button.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const dropdownWidth = 380;
    const maxDropdownHeight = 300;

    console.log('Button rect:', buttonRect);
    console.log('Viewport:', viewportWidth, 'x', viewportHeight);

    // 重置所有可能干扰的样式
    dropdown.style.inset = 'auto';
    dropdown.style.transform = 'none';
    dropdown.style.position = 'fixed';
    dropdown.style.zIndex = '10000';

    // 计算水平位置（优先显示在按钮左侧）
    let leftPos;
    const spaceLeft = buttonRect.left;
    const spaceRight = viewportWidth - buttonRect.right;

    if (spaceLeft >= dropdownWidth + 10) {
        // 左侧有足够空间
        leftPos = buttonRect.left - dropdownWidth - 10;
        console.log('Positioning to the left');
    } else if (spaceRight >= dropdownWidth + 10) {
        // 右侧有足够空间
        leftPos = buttonRect.right + 10;
        console.log('Positioning to the right');
    } else {
        // 空间不足，确保在视窗内
        leftPos = Math.max(10, Math.min(buttonRect.left - dropdownWidth/2, viewportWidth - dropdownWidth - 10));
        console.log('Positioning centered/adjusted');
    }

    // 计算垂直位置
    let topPos;
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;
    let finalHeight = maxDropdownHeight;

    if (spaceBelow >= maxDropdownHeight + 10) {
        // 向下显示
        topPos = buttonRect.bottom + 10;
        console.log('Positioning below');
    } else if (spaceAbove >= maxDropdownHeight + 10) {
        // 向上显示
        topPos = buttonRect.top - maxDropdownHeight - 10;
        console.log('Positioning above');
    } else {
        // 空间不足，选择空间较大的一侧并调整高度
        if (spaceBelow > spaceAbove) {
            topPos = buttonRect.bottom + 10;
            finalHeight = Math.max(150, spaceBelow - 20);
            console.log('Positioning below with reduced height');
        } else {
            finalHeight = Math.max(150, spaceAbove - 20);
            topPos = buttonRect.top - finalHeight - 10;
            console.log('Positioning above with reduced height');
        }
    }

    // 确保位置在视窗内
    leftPos = Math.max(10, Math.min(leftPos, viewportWidth - dropdownWidth - 10));
    topPos = Math.max(10, Math.min(topPos, viewportHeight - finalHeight - 10));

    console.log('Final position:', leftPos, topPos, 'Size:', dropdownWidth, finalHeight);

    // 应用计算出的位置
    dropdown.style.left = leftPos + 'px';
    dropdown.style.top = topPos + 'px';
    dropdown.style.width = dropdownWidth + 'px';
    dropdown.style.maxHeight = finalHeight + 'px';
    dropdown.style.right = 'auto';
    dropdown.style.bottom = 'auto';
}

// 全局变量
let notificationDropdownOpen = false;

// 切换通知下拉菜单
function toggleNotificationDropdown(event) {
    // 强制阻止所有事件传播
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    console.log('toggleNotificationDropdown called');

    const dropdown = document.querySelector('.notification-dropdown');
    const button = document.querySelector('#notificationDropdown');

    if (!dropdown || !button) {
        console.log('Elements not found');
        return false;
    }

    // 检查当前实际显示状态
    const computedStyle = window.getComputedStyle(dropdown);
    const isCurrentlyVisible = computedStyle.display !== 'none';

    console.log('Current visibility:', isCurrentlyVisible);

    if (isCurrentlyVisible) {
        // 关闭下拉菜单
        console.log('Closing dropdown');
        dropdown.style.display = 'none';
        button.setAttribute('aria-expanded', 'false');
        notificationDropdownOpen = false;
    } else {
        // 打开下拉菜单
        console.log('Opening dropdown');
        dropdown.style.display = 'block';
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        dropdown.style.zIndex = '10000';
        dropdown.style.position = 'fixed';
        button.setAttribute('aria-expanded', 'true');
        notificationDropdownOpen = true;

        // 检查样式是否生效
        setTimeout(function() {
            const newStyle = window.getComputedStyle(dropdown);
            const rect = dropdown.getBoundingClientRect();
            console.log('After setting display:');
            console.log('- display:', newStyle.display);
            console.log('- visibility:', newStyle.visibility);
            console.log('- opacity:', newStyle.opacity);
            console.log('- zIndex:', newStyle.zIndex);
            console.log('- position:', newStyle.position);
            console.log('- width:', newStyle.width);
            console.log('- height:', newStyle.height);
            console.log('- top:', newStyle.top);
            console.log('- left:', newStyle.left);
            console.log('- rect:', rect);
            console.log('- viewport size:', window.innerWidth, 'x', window.innerHeight);

            // 移除测试样式
            dropdown.style.backgroundColor = '';
            dropdown.style.border = '';
            console.log('Removed test styles');
        }, 10);

        // 调整位置
        setTimeout(function() {
            adjustNotificationPosition();
        }, 100);

        // 监听样式变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    console.log('Style changed:', dropdown.style.cssText);
                    if (dropdown.style.display === 'none') {
                        console.log('WARNING: Dropdown was hidden by external code!');
                        console.trace('Stack trace:');
                    }
                }
            });
        });
        observer.observe(dropdown, { attributes: true, attributeFilter: ['style'] });

        // 强制保持显示状态
        setTimeout(function() {
            if (dropdown.style.display === 'none') {
                console.log('Dropdown was hidden, forcing it back');
                dropdown.style.display = 'block';
            }
        }, 200);

        // 持续检查
        const checkInterval = setInterval(function() {
            if (dropdown.style.display === 'none') {
                console.log('Dropdown hidden at:', new Date().getTime());
                clearInterval(checkInterval);
            }
        }, 10);
    }

    return false;
}

// 监听通知按钮点击事件
document.addEventListener('DOMContentLoaded', function() {
    const notificationButton = document.querySelector('#notificationDropdown');
    const dropdown = document.querySelector('.notification-dropdown');

    if (notificationButton && dropdown) {
        // 完全移除Bootstrap的下拉菜单功能
        if (window.bootstrap && window.bootstrap.Dropdown) {
            const existingDropdown = window.bootstrap.Dropdown.getInstance(notificationButton);
            if (existingDropdown) {
                existingDropdown.dispose();
                console.log('Disposed existing Bootstrap dropdown');
            }
        }

        // 移除所有可能的事件监听器
        const newButton = notificationButton.cloneNode(true);
        notificationButton.parentNode.replaceChild(newButton, notificationButton);
        console.log('Replaced button to remove all event listeners');

        // 阻止下拉菜单内部点击事件冒泡
        dropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (notificationDropdownOpen) {
                adjustNotificationPosition();
            }
        });

        // 点击外部区域关闭通知中心
        document.addEventListener('click', function(event) {
            const computedStyle = window.getComputedStyle(dropdown);
            const isCurrentlyVisible = computedStyle.display !== 'none';
            if (isCurrentlyVisible) {
                if (!newButton.contains(event.target) && !dropdown.contains(event.target)) {
                    dropdown.style.display = 'none';
                    newButton.setAttribute('aria-expanded', 'false');
                    notificationDropdownOpen = false;
                }
            }
        });
    }
});

// 现代化Modal函数
function showConfirmModal(title, message, onConfirm) {
    document.getElementById('confirmModalTitle').textContent = title;
    document.getElementById('confirmModalMessage').textContent = message;

    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();

    // 绑定确认按钮事件
    document.getElementById('confirmModalBtn').onclick = function() {
        modal.hide();
        if (onConfirm) onConfirm();
    };
}

function showSuccessModal(title, message, onClose) {
    document.getElementById('successModalTitle').textContent = title;
    document.getElementById('successModalMessage').textContent = message;

    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();

    // 2秒后自动关闭并执行回调
    setTimeout(() => {
        modal.hide();
        if (onClose) onClose();
    }, 2000);
}

function showErrorModal(message) {
    document.getElementById('errorModalMessage').textContent = message;

    const modal = new bootstrap.Modal(document.getElementById('errorModal'));
    modal.show();
}

// 操作按钮相关函数
function confirmReceive(bugId) {
    showConfirmModal('确认接收', '确认接收此问题？', function() {
        fetch(`/bug/confirm/${bugId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessModal('接收成功！', '问题已成功接收', function() {
                    location.reload();
                });
            } else {
                showErrorModal('接收失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorModal('操作失败，请重试');
        });
    });
}

function confirmComplete(bugId) {
    showConfirmModal('确认完成', '确认此问题已完成闭环？', function() {
        fetch(`/bug/complete/${bugId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessModal('确认完成！', '问题已成功完成闭环', function() {
                    location.reload();
                });
            } else {
                showErrorModal('操作失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorModal('操作失败，请重试');
        });
    });
}

function deleteBug(bugId) {
    showConfirmModal('确认删除', '确认删除此问题？此操作不可撤销！', function() {
        fetch(`/bug/delete/${bugId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessModal('删除成功！', '问题已成功删除', function() {
                    location.reload();
                });
            } else {
                showErrorModal('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorModal('删除失败，请重试');
        });
    });
}
</script>
{% endblock %}