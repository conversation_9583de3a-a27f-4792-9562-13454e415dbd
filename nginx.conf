# Nginx 反向代理配置
server {
    # 监听80端口(HTTP)
    listen 80;
    # 替换为你的域名或服务器IP
    server_name your_domain.com;  

    # 主请求处理
    location / {
        # 转发请求到本地的Flask应用(运行在5000端口)
        proxy_pass http://localhost:5000;
        
        # 以下header保证应用能获取真实客户端信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 启用WebSocket支持(如果需要)
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 静态文件处理
    location /static/ {
        # 替换为你的静态文件实际路径
        alias /path/to/your/app/static/;
        
        # 缓存控制(30天)
        expires 30d;
        
        # 静态文件访问日志关闭(可选)
        access_log off;
    }

    # 错误页面配置(可选)
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
