
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost; # 或者您的域名，例如 example.com

        # 静态文件服务
        location /static/ {
            alias D:/app_data/repositories/ReBugTracker/static/; # 替换为您的实际静态文件路径
            expires 30d; # 缓存30天
            add_header Cache-Control "public, no-transform";
        }

        # 上传文件服务
        location /uploads/ {
            alias D:/app_data/repositories/ReBugTracker/uploads/; # 替换为您的实际上传文件路径
            expires 30d; # 缓存30天
            add_header Cache-Control "public, no-transform";
        }

        # 将所有其他请求代理到Waitress
        location / {
            proxy_pass http://127.0.0.1:5000; # Waitress监听的地址和端口
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_redirect off;
        }

        # 错误页面
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
